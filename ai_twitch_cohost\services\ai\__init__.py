"""
AI services module.

Provides AI integration services including OpenRouter, DeepSeek, and Ollama
with unified interface, cost tracking, and usage monitoring.
"""

from ai_twitch_cohost.core.service_container import ServiceContainer


async def register_services(container: ServiceContainer) -> None:
    """
    Register AI services with the service container.

    Args:
        container: Service container to register services in
    """
    try:
        from ai_twitch_cohost.services.ai.ai_service import AIService
        from ai_twitch_cohost.services.ai.rate_limiter import RateLimiter

        # Register rate limiter first (dependency for providers)
        container.register_singleton(RateLimiter)

        # Register main AI service (it will create providers internally)
        container.register_singleton(AIService)

        # Initialize the AI service
        ai_service = container.resolve(AIService)
        await ai_service.initialize()

    except ImportError as e:
        # Services not yet implemented
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"AI services not available: {e}")
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to register AI services: {e}")


# Module reference for application.py
import sys
ai_service_module = sys.modules[__name__]
