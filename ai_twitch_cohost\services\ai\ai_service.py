"""
Main AI service that orchestrates all AI providers.

Provides a unified interface for AI operations with automatic fallback,
cost tracking, and provider management.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from ai_twitch_cohost.core.logging_config import LoggerMixin
from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.event_bus import EventBus
from ai_twitch_cohost.services.ai.base_ai_provider import BaseAIProvider
from ai_twitch_cohost.services.ai.openrouter_provider import OpenRouterProvider
from ai_twitch_cohost.services.ai.deepseek_provider import DeepSeekProvider
from ai_twitch_cohost.services.ai.ollama_provider import OllamaProvider
from ai_twitch_cohost.services.ai.rate_limiter import RateLimiter
from ai_twitch_cohost.services.ai.models import (
    AIRequest, AIResponse, AIError, AIModelInfo, AIProviderType,
    ProviderStatus, UsageStats, ModelPerformance
)
from ai_twitch_cohost.events.ai_events import AIFallbackEvent


class AIService(LoggerMixin):
    """
    Main AI service that manages all AI providers.

    Features:
    - Unified interface for all AI providers
    - Automatic fallback between providers
    - Cost tracking and usage monitoring
    - Model discovery and management
    - Performance metrics
    """

    def __init__(
        self,
        config_manager: ConfigManager,
        event_bus: EventBus
    ):
        """Initialize the AI service."""
        super().__init__()
        self.config_manager = config_manager
        self.event_bus = event_bus

        # Initialize rate limiter
        self.rate_limiter = RateLimiter()

        # Initialize providers
        self.providers: Dict[str, BaseAIProvider] = {}
        self._initialize_providers()

        # Service state
        self._initialized = False
        self._available_models: Dict[str, AIModelInfo] = {}

    def _initialize_providers(self) -> None:
        """Initialize all AI providers."""
        self.providers = {
            AIProviderType.OPENROUTER.value: OpenRouterProvider(
                self.config_manager, self.event_bus, self.rate_limiter
            ),
            AIProviderType.DEEPSEEK.value: DeepSeekProvider(
                self.config_manager, self.event_bus, self.rate_limiter
            ),
            AIProviderType.OLLAMA.value: OllamaProvider(
                self.config_manager, self.event_bus, self.rate_limiter
            )
        }

    async def initialize(self) -> bool:
        """
        Initialize the AI service and all providers.

        Returns:
            True if at least one provider initialized successfully
        """
        self.logger.info("Initializing AI service...")

        # Initialize providers concurrently
        tasks = []
        for provider_name, provider in self.providers.items():
            task = asyncio.create_task(
                self._initialize_provider(provider_name, provider)
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Check results
        successful_providers = []
        for i, result in enumerate(results):
            provider_name = list(self.providers.keys())[i]
            if isinstance(result, bool) and result:
                successful_providers.append(provider_name)
            elif isinstance(result, Exception):
                self.logger.error(f"Failed to initialize {provider_name}: {result}")

        if successful_providers:
            self.logger.info(f"Initialized providers: {', '.join(successful_providers)}")
            self._initialized = True

            # Discover all models
            await self._discover_all_models()

            return True
        else:
            self.logger.error("No AI providers could be initialized")
            return False

    async def _initialize_provider(self, name: str, provider: BaseAIProvider) -> bool:
        """Initialize a single provider."""
        try:
            return await provider.initialize()
        except Exception as e:
            self.logger.error(f"Failed to initialize {name} provider: {e}")
            return False

    async def _discover_all_models(self) -> None:
        """Discover models from all providers."""
        self._available_models.clear()

        for provider in self.providers.values():
            if provider.status.status == "online":
                models = provider.available_models
                for model_id, model_info in models.items():
                    # Prefix model ID with provider name to avoid conflicts
                    full_model_id = f"{provider.name}:{model_id}"
                    self._available_models[full_model_id] = model_info

    async def generate_response(
        self,
        prompt: str,
        model: Optional[str] = None,
        user_id: Optional[str] = None,
        system_prompt: Optional[str] = None,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        **kwargs
    ) -> AIResponse:
        """
        Generate an AI response using the best available provider.

        Args:
            prompt: User prompt
            model: Specific model to use (optional)
            user_id: User ID for tracking (optional)
            system_prompt: System prompt (optional)
            conversation_history: Previous conversation (optional)
            **kwargs: Additional parameters

        Returns:
            AI response

        Raises:
            AIError: If all providers fail
        """
        if not self._initialized:
            raise AIError(
                error_type="not_initialized",
                message="AI service not initialized",
                provider="ai_service"
            )

        # Create request
        request = AIRequest(
            prompt=prompt,
            model=model,
            user_id=user_id,
            system_prompt=system_prompt,
            conversation_history=conversation_history or [],
            **kwargs
        )

        # Determine provider order
        provider_order = self._get_provider_order(model)

        # Try providers in order
        last_error = None
        for provider_name in provider_order:
            provider = self.providers.get(provider_name)

            if not provider or provider.status.status != "online":
                continue

            try:
                # Adjust model name for provider
                provider_request = self._adjust_request_for_provider(request, provider)

                response = await provider.make_request(provider_request)

                # Log successful provider if fallback occurred
                if provider_name != provider_order[0]:
                    await self.event_bus.emit(AIFallbackEvent(
                        from_provider=provider_order[0],
                        to_provider=provider_name,
                        reason="Primary provider failed",
                        request_id=request.request_id
                    ))

                return response

            except AIError as e:
                last_error = e
                self.logger.warning(
                    f"Provider {provider_name} failed: {e.message}"
                )

                # If this was a rate limit error, don't try other providers immediately
                if e.error_type == "rate_limited":
                    break

                continue

        # All providers failed
        if last_error:
            raise last_error
        else:
            raise AIError(
                error_type="no_providers",
                message="No AI providers available",
                provider="ai_service"
            )

    def _get_provider_order(self, model: Optional[str] = None) -> List[str]:
        """
        Get the order of providers to try based on configuration and model.

        Args:
            model: Requested model (optional)

        Returns:
            List of provider names in order of preference
        """
        config = self.config_manager.get_user_settings().ai

        # If a specific model is requested, check which provider has it
        if model and ":" in model:
            provider_name, _ = model.split(":", 1)
            if provider_name in self.providers:
                return [provider_name]

        # Use configured order
        order = [config.primary_provider.value]

        if config.fallback_provider:
            order.append(config.fallback_provider.value)

        # Add remaining providers
        for provider_name in self.providers.keys():
            if provider_name not in order:
                order.append(provider_name)

        return order

    def _adjust_request_for_provider(
        self,
        request: AIRequest,
        provider: BaseAIProvider
    ) -> AIRequest:
        """
        Adjust request for a specific provider.

        Args:
            request: Original request
            provider: Target provider

        Returns:
            Adjusted request
        """
        # Create a copy of the request
        provider_request = AIRequest(**request.model_dump())

        # Adjust model name
        if request.model and ":" in request.model:
            _, model_name = request.model.split(":", 1)
            provider_request.model = model_name

        return provider_request

    def get_available_models(self) -> Dict[str, AIModelInfo]:
        """Get all available models from all providers."""
        return self._available_models.copy()

    def get_provider_status(self) -> Dict[str, ProviderStatus]:
        """Get status of all providers."""
        return {
            name: provider.status
            for name, provider in self.providers.items()
        }

    def get_usage_stats(self) -> Dict[str, Dict[str, UsageStats]]:
        """Get usage statistics from all providers."""
        stats = {}
        for name, provider in self.providers.items():
            stats[name] = provider.get_usage_stats()
        return stats

    def get_performance_metrics(self) -> Dict[str, Dict[str, ModelPerformance]]:
        """Get performance metrics from all providers."""
        metrics = {}
        for name, provider in self.providers.items():
            metrics[name] = provider.get_performance_metrics()
        return metrics

    def get_rate_limit_status(self) -> Dict[str, Dict[str, float]]:
        """Get rate limit status for all buckets."""
        return self.rate_limiter.get_all_buckets()

    async def refresh_models(self) -> None:
        """Refresh model lists from all providers."""
        tasks = []
        for provider in self.providers.values():
            if provider.status.status == "online":
                task = asyncio.create_task(provider.discover_models())
                tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            await self._discover_all_models()

    async def cleanup(self) -> None:
        """Cleanup all providers and resources."""
        self.logger.info("Cleaning up AI service...")

        # Cleanup providers
        tasks = []
        for provider in self.providers.values():
            if hasattr(provider, 'cleanup'):
                task = asyncio.create_task(provider.cleanup())
                tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self._initialized = False
        self.logger.info("AI service cleaned up")
