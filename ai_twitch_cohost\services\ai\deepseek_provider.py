"""
DeepSeek AI provider implementation.

Provides direct integration with DeepSeek API as a fallback option
from OpenRouter with cost comparison capabilities.
"""

import httpx
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.event_bus import EventBus
from ai_twitch_cohost.services.ai.base_ai_provider import BaseAIProvider
from ai_twitch_cohost.services.ai.models import (
    AIRequest, AIResponse, AIError, AIModelInfo, AIProviderType
)
from ai_twitch_cohost.services.ai.rate_limiter import RateLimiter
from ai_twitch_cohost.events.ai_events import AIModelDiscoveredEvent


class DeepSeekProvider(BaseAIProvider):
    """DeepSeek AI provider implementation."""

    def __init__(
        self,
        config_manager: ConfigManager,
        event_bus: EventBus,
        rate_limiter: RateLimiter
    ):
        """Initialize DeepSeek provider."""
        super().__init__(AIProviderType.DEEPSEEK, config_manager, event_bus, rate_limiter)
        self._client: Optional[httpx.AsyncClient] = None
        self._config = None

        # DeepSeek pricing (as of 2024)
        self._pricing = {
            "deepseek-chat": {"prompt": 0.00014, "completion": 0.00028},  # $0.14/$0.28 per 1M tokens
            "deepseek-coder": {"prompt": 0.00014, "completion": 0.00028}
        }

    def _setup_rate_limiting(self) -> None:
        """Setup rate limiting buckets for DeepSeek."""
        # DeepSeek rate limits (conservative estimates)
        self.rate_limiter.add_bucket(
            f"{self.name}_requests",
            capacity=60,  # 60 requests per minute
            refill_rate=1.0  # 1 request per second
        )

        self.rate_limiter.add_bucket(
            f"{self.name}_tokens",
            capacity=30000,  # 30k tokens per minute
            refill_rate=500  # 500 tokens per second
        )

    async def initialize(self) -> bool:
        """Initialize the DeepSeek provider."""
        try:
            self._config = self.config_manager.get_user_settings().ai.deepseek

            if not self._config.api_key:
                self.logger.error("DeepSeek API key not configured")
                await self.update_status("error", "API key not configured")
                return False

            # Create HTTP client
            self._client = httpx.AsyncClient(
                base_url=self._config.base_url,
                headers={
                    "Authorization": f"Bearer {self._config.api_key.get_secret_value()}",
                    "Content-Type": "application/json"
                },
                timeout=self._config.timeout_seconds
            )

            # Test connection and set up models
            if await self.check_health():
                await self.discover_models()
                await self.update_status("online")
                self.logger.info("DeepSeek provider initialized successfully")
                return True
            else:
                await self.update_status("error", "Health check failed")
                return False

        except Exception as e:
            self.logger.error(f"Failed to initialize DeepSeek provider: {e}")
            await self.update_status("error", str(e))
            return False

    async def discover_models(self) -> List[AIModelInfo]:
        """Discover available models from DeepSeek."""
        # DeepSeek has a limited set of known models
        models_data = [
            {
                "id": "deepseek-chat",
                "name": "DeepSeek Chat",
                "description": "DeepSeek's general-purpose chat model",
                "context_length": 32768,
                "capabilities": ["chat", "reasoning", "analysis"]
            },
            {
                "id": "deepseek-coder",
                "name": "DeepSeek Coder",
                "description": "DeepSeek's specialized coding model",
                "context_length": 16384,
                "capabilities": ["coding", "debugging", "code-review"]
            }
        ]

        models = []

        for model_data in models_data:
            model_info = AIModelInfo(
                id=model_data["id"],
                name=model_data["name"],
                description=model_data["description"],
                context_length=model_data["context_length"],
                pricing=self._pricing.get(model_data["id"]),
                capabilities=model_data["capabilities"],
                provider="deepseek",
                available=True
            )
            models.append(model_info)
            self._available_models[model_info.id] = model_info

        self.logger.info(f"Configured {len(models)} DeepSeek models")

        # Emit model discovery event
        await self.event_bus.emit(AIModelDiscoveredEvent(
            provider=self.name,
            models={model.id: model.dict() for model in models}
        ))

        return models

    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate a response using DeepSeek."""
        if not self._client:
            raise AIError(
                error_type="not_initialized",
                message="DeepSeek provider not initialized",
                provider=self.name
            )

        # Use configured model or default
        model = request.model or self._config.model

        # Build messages
        messages = []

        # Add system prompt if provided
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})

        # Add conversation history
        for msg in request.conversation_history:
            messages.append(msg)

        # Add current prompt
        messages.append({"role": "user", "content": request.prompt})

        # Prepare request data
        request_data = {
            "model": model,
            "messages": messages,
            "max_tokens": request.max_tokens or self._config.max_tokens,
            "temperature": request.temperature or self._config.temperature,
            "stream": False
        }

        try:
            # Make the API call
            response = await self._client.post("/chat/completions", json=request_data)
            response.raise_for_status()

            response_data = response.json()

            # Extract response content
            if not response_data.get("choices"):
                raise AIError(
                    error_type="no_response",
                    message="No response choices returned",
                    provider=self.name,
                    model=model
                )

            choice = response_data["choices"][0]
            content = choice["message"]["content"]

            # Extract usage information
            usage = response_data.get("usage", {})
            tokens_used = usage.get("total_tokens", 0)
            prompt_tokens = usage.get("prompt_tokens", 0)
            completion_tokens = usage.get("completion_tokens", 0)

            # Calculate cost
            cost = self._calculate_cost(model, prompt_tokens, completion_tokens)

            return AIResponse(
                content=content,
                model=model,
                provider=self.name,
                tokens_used=tokens_used,
                cost=cost,
                response_time_ms=0,  # Will be set by base class
                request_id=request.request_id,
                finish_reason=choice.get("finish_reason"),
                metadata={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "usage": usage
                }
            )

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP {e.response.status_code}: {e.response.text}"
            raise AIError(
                error_type="http_error",
                message=error_msg,
                code=str(e.response.status_code),
                provider=self.name,
                model=model,
                retryable=e.response.status_code in [429, 500, 502, 503, 504]
            )
        except Exception as e:
            raise AIError(
                error_type="request_failed",
                message=str(e),
                provider=self.name,
                model=model
            )

    async def check_health(self) -> bool:
        """Check if DeepSeek is healthy and available."""
        if not self._client:
            return False

        try:
            # Test with a simple request
            test_data = {
                "model": self._config.model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }

            response = await self._client.post("/chat/completions", json=test_data, timeout=10.0)
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"DeepSeek health check failed: {e}")
            return False

    def _calculate_cost(self, model: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Calculate cost for DeepSeek request."""
        pricing = self._pricing.get(model)

        if not pricing:
            return 0.0

        prompt_cost = prompt_tokens * pricing["prompt"] / 1000
        completion_cost = completion_tokens * pricing["completion"] / 1000

        return prompt_cost + completion_cost

    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self._client:
            await self._client.aclose()
            self._client = None

        await self.update_status("offline")
        self.logger.info("DeepSeek provider cleaned up")
