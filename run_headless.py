#!/usr/bin/env python3
"""
Headless launcher for AI Twitch Co-Host.

This script runs the application without GUI to demonstrate the core architecture.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Force headless mode by temporarily removing PyQt6
original_path = sys.path.copy()
sys.modules_backup = {}

# Backup and remove PyQt6 modules if they exist
pyqt_modules = [name for name in sys.modules.keys() if name.startswith('PyQt6') or name.startswith('qfluentwidgets')]
for module_name in pyqt_modules:
    sys.modules_backup[module_name] = sys.modules.pop(module_name, None)

try:
    # Now import and run the application
    from ai_twitch_cohost.__main__ import main
    import asyncio
    
    print("🚀 Running AI Twitch Co-Host in headless mode...")
    print("This demonstrates the core architecture without GUI dependencies.")
    print("Press Ctrl+C to stop the application.")
    print("-" * 60)
    
    # Run the application
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
    
except KeyboardInterrupt:
    print("\n✅ Application stopped by user")
    sys.exit(0)
except Exception as e:
    print(f"❌ Application failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
finally:
    # Restore modules
    for module_name, module in sys.modules_backup.items():
        if module is not None:
            sys.modules[module_name] = module
