#!/usr/bin/env python3
"""
Minimal GUI test for AI Twitch Co-Host.

This script tests if the basic PyQt6 + Fluent Widgets setup works.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt

    # Set high DPI scaling BEFORE any imports that might create widgets
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # Create application BEFORE importing qfluentwidgets
    app = QApplication(sys.argv)
    app.setApplicationName("Test App")

    print("✓ QApplication created successfully")

    # Now import qfluentwidgets after QApplication is created
    from qfluentwidgets import FluentWindow, FluentIcon, NavigationItemPosition

    print("✓ PyQt6 and qfluentwidgets imports successful")

    # Create main window
    window = FluentWindow()
    window.setWindowTitle("AI Twitch Co-Host - Test")
    window.resize(800, 600)

    print("✓ FluentWindow created successfully")

    # Show window
    window.show()

    print("✓ Window shown successfully")
    print("🎉 GUI test successful! Close the window to exit.")

    # Run event loop
    sys.exit(app.exec())

except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ GUI test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
