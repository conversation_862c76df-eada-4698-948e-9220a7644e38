"""
Chat Monitor page for AI Twitch Co-Host.

This module provides the chat monitoring interface for viewing and
managing Twitch chat interactions.
"""

import logging
from typing import Optional
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame, QTextEdit, QHBoxLayout
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.service_container import ServiceContainer
from ai_twitch_cohost.core.event_bus import EventBus


class ChatMonitorPage(QWidget):
    """
    Chat monitoring page for the AI Twitch Co-Host application.
    
    Displays:
    - Live chat messages
    - Chat statistics
    - Moderation tools
    """
    
    def __init__(
        self,
        config_manager: ConfigManager,
        service_container: ServiceContainer,
        event_bus: EventBus,
        parent: Optional[QWidget] = None
    ):
        """
        Initialize the chat monitor page.
        
        Args:
            config_manager: Configuration manager instance
            service_container: Service container for dependency injection
            event_bus: Event bus for inter-component communication
            parent: Parent widget
        """
        super().__init__(parent)
        
        self.config_manager = config_manager
        self.service_container = service_container
        self.event_bus = event_bus
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self._setup_ui()
        
        self.logger.debug("Chat Monitor page initialized")
    
    def _setup_ui(self) -> None:
        """Setup the chat monitor UI."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title_label = QLabel("Chat Monitor")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Chat display section
        chat_frame = self._create_chat_section()
        layout.addWidget(chat_frame)
        
        # Statistics section
        stats_frame = self._create_stats_section()
        layout.addWidget(stats_frame)
    
    def _create_chat_section(self) -> QFrame:
        """Create the chat display section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        frame.setLineWidth(1)
        
        layout = QVBoxLayout(frame)
        
        # Section title
        title = QLabel("Live Chat")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)
        
        # Chat display
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setPlaceholderText("Chat messages will appear here when connected to Twitch...")
        self.chat_display.setMinimumHeight(300)
        layout.addWidget(self.chat_display)
        
        return frame
    
    def _create_stats_section(self) -> QFrame:
        """Create the chat statistics section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        frame.setLineWidth(1)
        
        layout = QVBoxLayout(frame)
        
        # Section title
        title = QLabel("Chat Statistics")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)
        
        # Stats layout
        stats_layout = QHBoxLayout()
        
        # Messages count
        messages_label = QLabel("Messages: 0")
        stats_layout.addWidget(messages_label)
        
        # Users count
        users_label = QLabel("Active Users: 0")
        stats_layout.addWidget(users_label)
        
        # Commands count
        commands_label = QLabel("Commands: 0")
        stats_layout.addWidget(commands_label)
        
        stats_layout.addStretch()
        layout.addLayout(stats_layout)
        
        return frame
