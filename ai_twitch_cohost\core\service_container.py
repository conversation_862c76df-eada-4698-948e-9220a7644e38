"""
Dependency injection container for managing application services.

Provides a centralized container for registering and resolving dependencies,
supporting singleton and transient lifetimes, factory functions, and
automatic dependency resolution.
"""

import inspect
from typing import Dict, Any, Type, TypeVar, Callable, Optional, Union, get_type_hints
from enum import Enum
import logging

from ai_twitch_cohost.core.logging_config import LoggerMixin


T = TypeVar('T')


class ServiceLifetime(Enum):
    """Service lifetime options."""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceRegistration:
    """Service registration information."""
    
    def __init__(
        self,
        service_type: Type,
        implementation: Union[Type, Callable],
        lifetime: ServiceLifetime,
        factory: Optional[Callable] = None,
        instance: Optional[Any] = None
    ):
        self.service_type = service_type
        self.implementation = implementation
        self.lifetime = lifetime
        self.factory = factory
        self.instance = instance


class ServiceContainer(LoggerMixin):
    """
    Dependency injection container for managing application services.
    
    Features:
    - Service registration with different lifetimes
    - Automatic dependency resolution
    - Factory function support
    - Circular dependency detection
    - Service disposal for cleanup
    """
    
    def __init__(self):
        """Initialize the service container."""
        self._services: Dict[Type, ServiceRegistration] = {}
        self._instances: Dict[Type, Any] = {}
        self._resolving: set = set()  # For circular dependency detection
        self._disposing: bool = False  # For recursive disposal protection
        super().__init__()  # Initialize LoggerMixin
    
    def register_singleton(
        self,
        service_type: Type[T],
        implementation: Optional[Union[Type[T], Callable[[], T]]] = None,
        factory: Optional[Callable[[], T]] = None,
        instance: Optional[T] = None
    ) -> 'ServiceContainer':
        """
        Register a singleton service.
        
        Args:
            service_type: The service interface/type
            implementation: The implementation class or factory function
            factory: Factory function to create the service
            instance: Pre-created instance to use
            
        Returns:
            Self for method chaining
        """
        return self._register(
            service_type,
            implementation or service_type,
            ServiceLifetime.SINGLETON,
            factory,
            instance
        )
    
    def register_transient(
        self,
        service_type: Type[T],
        implementation: Optional[Union[Type[T], Callable[[], T]]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'ServiceContainer':
        """
        Register a transient service (new instance each time).
        
        Args:
            service_type: The service interface/type
            implementation: The implementation class or factory function
            factory: Factory function to create the service
            
        Returns:
            Self for method chaining
        """
        return self._register(
            service_type,
            implementation or service_type,
            ServiceLifetime.TRANSIENT,
            factory
        )
    
    def register_scoped(
        self,
        service_type: Type[T],
        implementation: Optional[Union[Type[T], Callable[[], T]]] = None,
        factory: Optional[Callable[[], T]] = None
    ) -> 'ServiceContainer':
        """
        Register a scoped service (one instance per scope).
        
        Args:
            service_type: The service interface/type
            implementation: The implementation class or factory function
            factory: Factory function to create the service
            
        Returns:
            Self for method chaining
        """
        return self._register(
            service_type,
            implementation or service_type,
            ServiceLifetime.SCOPED,
            factory
        )
    
    def _register(
        self,
        service_type: Type,
        implementation: Union[Type, Callable],
        lifetime: ServiceLifetime,
        factory: Optional[Callable] = None,
        instance: Optional[Any] = None
    ) -> 'ServiceContainer':
        """Internal method to register a service."""
        registration = ServiceRegistration(
            service_type=service_type,
            implementation=implementation,
            lifetime=lifetime,
            factory=factory,
            instance=instance
        )
        
        self._services[service_type] = registration
        
        # If it's a singleton with a pre-created instance, store it
        if lifetime == ServiceLifetime.SINGLETON and instance is not None:
            self._instances[service_type] = instance
        
        self.logger.debug(
            f"Registered {service_type.__name__} as {lifetime.value}"
        )
        
        return self
    
    def resolve(self, service_type: Type[T]) -> T:
        """
        Resolve a service instance.
        
        Args:
            service_type: The service type to resolve
            
        Returns:
            Service instance
            
        Raises:
            ValueError: If service is not registered or circular dependency detected
        """
        # Check for circular dependencies
        if service_type in self._resolving:
            raise ValueError(f"Circular dependency detected for {service_type.__name__}")
        
        # Check if service is registered
        if service_type not in self._services:
            raise ValueError(f"Service {service_type.__name__} is not registered")
        
        registration = self._services[service_type]
        
        # For singletons, return existing instance if available
        if (registration.lifetime == ServiceLifetime.SINGLETON and 
            service_type in self._instances):
            return self._instances[service_type]
        
        # Mark as resolving for circular dependency detection
        self._resolving.add(service_type)
        
        try:
            # Create the instance
            instance = self._create_instance(registration)
            
            # Store singleton instances
            if registration.lifetime == ServiceLifetime.SINGLETON:
                self._instances[service_type] = instance
            
            return instance
        
        finally:
            # Remove from resolving set
            self._resolving.discard(service_type)
    
    def _create_instance(self, registration: ServiceRegistration) -> Any:
        """Create an instance based on the registration."""
        # Use pre-created instance if available
        if registration.instance is not None:
            return registration.instance
        
        # Use factory function if provided
        if registration.factory is not None:
            return self._call_with_injection(registration.factory)
        
        # Use implementation class/function
        if inspect.isclass(registration.implementation):
            return self._create_class_instance(registration.implementation)
        elif callable(registration.implementation):
            return self._call_with_injection(registration.implementation)
        else:
            raise ValueError(
                f"Invalid implementation for {registration.service_type.__name__}"
            )
    
    def _create_class_instance(self, cls: Type) -> Any:
        """Create an instance of a class with dependency injection."""
        # Get constructor signature
        init_signature = inspect.signature(cls.__init__)
        
        # Resolve constructor parameters
        kwargs = {}
        for param_name, param in init_signature.parameters.items():
            if param_name == 'self':
                continue
            
            # Try to resolve parameter type
            param_type = param.annotation
            if param_type != inspect.Parameter.empty:
                try:
                    kwargs[param_name] = self.resolve(param_type)
                except ValueError:
                    # If we can't resolve and there's no default, raise error
                    if param.default == inspect.Parameter.empty:
                        raise ValueError(
                            f"Cannot resolve parameter '{param_name}' of type "
                            f"{param_type} for {cls.__name__}"
                        )
        
        return cls(**kwargs)
    
    def _call_with_injection(self, func: Callable) -> Any:
        """Call a function with dependency injection."""
        # Get function signature
        signature = inspect.signature(func)
        
        # Resolve function parameters
        kwargs = {}
        for param_name, param in signature.parameters.items():
            param_type = param.annotation
            if param_type != inspect.Parameter.empty:
                try:
                    kwargs[param_name] = self.resolve(param_type)
                except ValueError:
                    # If we can't resolve and there's no default, raise error
                    if param.default == inspect.Parameter.empty:
                        raise ValueError(
                            f"Cannot resolve parameter '{param_name}' of type "
                            f"{param_type} for function {func.__name__}"
                        )
        
        return func(**kwargs)
    
    def is_registered(self, service_type: Type) -> bool:
        """
        Check if a service type is registered.
        
        Args:
            service_type: The service type to check
            
        Returns:
            True if registered, False otherwise
        """
        return service_type in self._services
    
    def get_registration(self, service_type: Type) -> Optional[ServiceRegistration]:
        """
        Get the registration for a service type.
        
        Args:
            service_type: The service type
            
        Returns:
            ServiceRegistration if found, None otherwise
        """
        return self._services.get(service_type)
    
    def get_registered_services(self) -> Dict[Type, ServiceRegistration]:
        """
        Get all registered services.
        
        Returns:
            Dictionary of service types to registrations
        """
        return self._services.copy()
    
    def dispose(self) -> None:
        """Dispose of all singleton instances that support disposal."""
        if self._disposing:
            return  # Prevent recursive disposal
            
        self._disposing = True
        try:
            # Create a copy of items to avoid modification during iteration
            instances_to_dispose = list(self._instances.items())
            
            for service_type, instance in instances_to_dispose:
                # Skip disposing of the container itself to prevent recursion
                if instance is self:
                    continue
                    
                try:
                    # Log before attempting disposal
                    self.logger.debug(f"Disposing service: {service_type.__name__}")
                    
                    if hasattr(instance, 'dispose'):
                        instance.dispose()
                    elif hasattr(instance, 'close'):
                        instance.close()
                    elif hasattr(instance, 'shutdown'):
                        instance.shutdown()
                        
                    self.logger.debug(f"Successfully disposed service: {service_type.__name__}")
                except Exception as e:
                    self.logger.error(
                        f"Error disposing service {service_type.__name__}: {e}",
                        exc_info=True
                    )
            
            # Clear collections in specific order
            self._instances.clear()
            self._services.clear()
            self._resolving.clear()
            self.logger.info("Service container disposed")
        finally:
            self._disposing = False


# Decorator for automatic service registration
def service(
    lifetime: ServiceLifetime = ServiceLifetime.TRANSIENT,
    service_type: Optional[Type] = None
):
    """
    Decorator for automatic service registration.
    
    Args:
        lifetime: Service lifetime
        service_type: Service interface type (defaults to the decorated class)
    """
    def decorator(cls: Type):
        # Store service metadata on the class
        cls._service_lifetime = lifetime
        cls._service_type = service_type or cls
        return cls
    
    return decorator


def auto_register_services(container: ServiceContainer, module) -> None:
    """
    Automatically register services from a module based on decorators.
    
    Args:
        container: Service container to register services in
        module: Module to scan for services
    """
    for attr_name in dir(module):
        attr = getattr(module, attr_name)
        if (inspect.isclass(attr) and 
            hasattr(attr, '_service_lifetime') and 
            hasattr(attr, '_service_type')):
            
            lifetime = attr._service_lifetime
            service_type = attr._service_type
            
            if lifetime == ServiceLifetime.SINGLETON:
                container.register_singleton(service_type, attr)
            elif lifetime == ServiceLifetime.TRANSIENT:
                container.register_transient(service_type, attr)
            elif lifetime == ServiceLifetime.SCOPED:
                container.register_scoped(service_type, attr)
