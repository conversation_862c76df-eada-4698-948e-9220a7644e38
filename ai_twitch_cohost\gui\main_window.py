"""
Main application window with navigation interface and tabbed layout.

Provides the primary user interface for the AI Twitch Co-Host application,
featuring a modern Fluent Design interface with navigation tabs for different
functional areas.
"""

import asyncio
import logging
from typing import Optional
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QTimer
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget
from PyQt6.QtGui import QIcon, QCloseEvent
from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.service_container import ServiceContainer
from ai_twitch_cohost.core.event_bus import EventBus
from ai_twitch_cohost import APP_NAME, APP_VERSION
from ai_twitch_cohost.gui.compatibility import (
    CompatibleMainWindow,
    is_fluent_widgets_available,
    is_system_tray_available,
    get_fluent_icon,
    get_navigation_position
)


class MainWindow(CompatibleMainWindow):
    """
    Main application window with navigation interface.

    Features:
    - Fluent Design navigation interface
    - Tabbed layout for different functional areas
    - System tray integration
    - Theme support
    - Service status monitoring
    """

    # Signals
    closing = pyqtSignal()

    def __init__(
        self,
        config_manager: ConfigManager,
        service_container: ServiceContainer,
        event_bus: EventBus,
        parent: Optional[QWidget] = None
    ):
        """
        Initialize the main window.

        Args:
            config_manager: Configuration manager instance
            service_container: Service container for dependency injection
            event_bus: Event bus for inter-component communication
            parent: Parent widget
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.service_container = service_container
        self.event_bus = event_bus

        # Setup logging
        self.logger = logging.getLogger(self.__class__.__name__)

        # Window state
        self._is_closing = False

        # System tray
        self.system_tray = None  # Optional[SystemTrayIcon] when available

        # Initialize UI
        self._setup_window()
        self._create_navigation_interface()
        self._setup_system_tray()
        self._connect_signals()

        # Load window state from configuration
        self._load_window_state()

        self.logger.info("Main window initialized")

    def _setup_window(self) -> None:
        """Setup basic window properties."""
        # Set window title and icon
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")

        # Set window size and position
        self.resize(1200, 800)

        # Set minimum size
        self.setMinimumSize(800, 600)

        # Center window on screen
        self._center_window()

    def _center_window(self) -> None:
        """Center the window on the screen."""
        from PyQt6.QtWidgets import QApplication

        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.availableGeometry()
            window_geometry = self.frameGeometry()
            center_point = screen_geometry.center()
            window_geometry.moveCenter(center_point)
            top_left = window_geometry.topLeft()
            self.move(top_left.x(), top_left.y())

    def _create_navigation_interface(self) -> None:
        """Create the navigation interface with tabs."""
        try:
            # Dashboard tab
            from ai_twitch_cohost.gui.pages.dashboard import DashboardPage
            dashboard_page = DashboardPage(
                self.config_manager,
                self.service_container,
                self.event_bus
            )
            dashboard_page.setObjectName("DashboardPage")
            self.addSubInterface(
                dashboard_page,
                get_fluent_icon("HOME"),
                "Dashboard",
                get_navigation_position("TOP")
            )

            # Chat Monitor tab
            from ai_twitch_cohost.gui.pages.chat_monitor import ChatMonitorPage
            chat_page = ChatMonitorPage(
                self.config_manager,
                self.service_container,
                self.event_bus
            )
            chat_page.setObjectName("ChatMonitorPage")
            self.addSubInterface(
                chat_page,
                get_fluent_icon("CHAT"),
                "Chat Monitor",
                get_navigation_position("TOP")
            )

            # Configuration tab
            from ai_twitch_cohost.gui.pages.configuration import ConfigurationPage
            config_page = ConfigurationPage(
                self.config_manager,
                self.service_container,
                self.event_bus
            )
            config_page.setObjectName("ConfigurationPage")
            self.addSubInterface(
                config_page,
                get_fluent_icon("SETTING"),
                "Configuration",
                get_navigation_position("TOP")
            )

            # Plugins tab
            from ai_twitch_cohost.gui.pages.plugins import PluginsPage
            plugins_page = PluginsPage(
                self.config_manager,
                self.service_container,
                self.event_bus
            )
            plugins_page.setObjectName("PluginsPage")
            self.addSubInterface(
                plugins_page,
                get_fluent_icon("APPLICATION"),
                "Plugins",
                get_navigation_position("TOP")
            )

            # Logs tab
            from ai_twitch_cohost.gui.pages.logs import LogsPage
            logs_page = LogsPage(
                self.config_manager,
                self.service_container,
                self.event_bus
            )
            logs_page.setObjectName("LogsPage")
            self.addSubInterface(
                logs_page,
                get_fluent_icon("DOCUMENT"),
                "Logs",
                get_navigation_position("BOTTOM")
            )

        except ImportError as e:
            self.logger.warning(f"Some GUI pages not available: {e}")
            # Create placeholder pages
            self._create_placeholder_pages()

    def _test_pages_individually(self) -> None:
        """Test pages one by one to identify the problematic one."""
        # Test with direct FluentWindow usage to bypass compatibility layer
        self.logger.info("Testing with direct FluentWindow...")

        # Check if we have a FluentWindow
        if hasattr(self, '_fluent_window') and self._fluent_window:
            try:
                # Test Dashboard page first
                self.logger.info("Testing Dashboard page...")
                from ai_twitch_cohost.gui.pages.dashboard import DashboardPage
                from qfluentwidgets import FluentIcon, NavigationItemPosition

                dashboard_page = DashboardPage(
                    self.config_manager,
                    self.service_container,
                    self.event_bus
                )
                dashboard_page.setObjectName("DashboardPage")

                # Add directly to FluentWindow
                self._fluent_window.addSubInterface(
                    dashboard_page,
                    FluentIcon.HOME,
                    "Dashboard",
                    NavigationItemPosition.TOP
                )
                self.logger.info("✓ Dashboard page added successfully")

            except Exception as e:
                self.logger.error(f"❌ Dashboard page failed: {e}")
                import traceback
                traceback.print_exc()
                # Fall back to placeholder
                self._add_placeholder_dashboard()
        else:
            self.logger.warning("No FluentWindow available, using placeholder")
            self._add_placeholder_dashboard()

    def _add_placeholder_dashboard(self) -> None:
        """Add placeholder dashboard page."""
        from ai_twitch_cohost.gui.pages.placeholder import PlaceholderPage
        page = PlaceholderPage("Dashboard")
        page.setObjectName("DashboardPage")
        self.addSubInterface(
            page,
            get_fluent_icon("HOME"),
            "Dashboard",
            get_navigation_position("TOP")
        )

    def _create_placeholder_pages(self) -> None:
        """Create placeholder pages when full pages are not available."""
        from ai_twitch_cohost.gui.pages.placeholder import PlaceholderPage

        pages = [
            ("Dashboard", "HOME", "TOP"),
            ("Chat Monitor", "CHAT", "TOP"),
            ("Configuration", "SETTING", "TOP"),
            ("Plugins", "APPLICATION", "TOP"),
            ("Logs", "DOCUMENT", "BOTTOM"),
        ]

        for name, icon_name, position_name in pages:
            page = PlaceholderPage(name)
            page.setObjectName(f"{name.replace(' ', '')}Page")
            self.addSubInterface(
                page,
                get_fluent_icon(icon_name),
                name,
                get_navigation_position(position_name)
            )

    def _setup_system_tray(self) -> None:
        """Setup system tray integration."""
        if not is_system_tray_available():
            self.logger.warning("System tray not available")
            return

        try:
            settings = self.config_manager.get_user_settings()
            if not settings.ui.minimize_to_tray:
                return

            # Import SystemTrayIcon when needed
            from ai_twitch_cohost.gui.compatibility import SystemTrayIcon
            if not SystemTrayIcon:
                self.logger.warning("SystemTrayIcon not available")
                return

            # Create system tray icon
            self.system_tray = SystemTrayIcon(self)
            self.system_tray.setIcon(self.windowIcon())
            self.system_tray.setToolTip(f"{APP_NAME} v{APP_VERSION}")

            # Create tray menu
            self._create_tray_menu()

            # Connect signals
            self.system_tray.activated.connect(self._on_tray_activated)

            # Show tray icon
            self.system_tray.show()

            self.logger.debug("System tray initialized")

        except Exception as e:
            self.logger.error(f"Failed to setup system tray: {e}")

    def _create_tray_menu(self) -> None:
        """Create the system tray context menu."""
        if not self.system_tray:
            return

        # Import Action when needed
        from ai_twitch_cohost.gui.compatibility import Action
        if not Action:
            # Fallback to PyQt6 QAction
            from PyQt6.QtGui import QAction as Action

        # Show/Hide action
        show_action = Action("Show", self)
        show_action.triggered.connect(self.show_window)
        self.system_tray.menu.addAction(show_action)

        # Separator
        self.system_tray.menu.addSeparator()

        # Quit action
        quit_action = Action("Quit", self)
        quit_action.triggered.connect(self.close_application)
        self.system_tray.menu.addAction(quit_action)

    def _connect_signals(self) -> None:
        """Connect window signals."""
        # Connect configuration change callback
        self.config_manager.register_change_callback(self._on_config_changed)

    def _load_window_state(self) -> None:
        """Load window state from configuration."""
        try:
            settings = self.config_manager.get_user_settings()
            ui_config = settings.ui

            # Set window size
            self.resize(ui_config.window_width, ui_config.window_height)

            # Set window position if specified
            if ui_config.window_x is not None and ui_config.window_y is not None:
                self.move(ui_config.window_x, ui_config.window_y)

            # Set maximized state
            if ui_config.maximized:
                self.showMaximized()

            # Set always on top
            if ui_config.always_on_top:
                self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint, True)

        except Exception as e:
            self.logger.error(f"Failed to load window state: {e}")

    def _save_window_state(self) -> None:
        """Save current window state to configuration."""
        try:
            settings = self.config_manager.get_user_settings()

            # Update window state
            if not self.isMaximized():
                settings.ui.window_width = self.width()
                settings.ui.window_height = self.height()
                settings.ui.window_x = self.x()
                settings.ui.window_y = self.y()

            settings.ui.maximized = self.isMaximized()

            # Save configuration
            import asyncio
            asyncio.create_task(self.config_manager.save_config())

        except Exception as e:
            self.logger.error(f"Failed to save window state: {e}")

    def show_window(self) -> None:
        """Show and raise the window."""
        self.show()
        self.raise_()
        self.activateWindow()

    def close_application(self) -> None:
        """Close the application completely."""
        self._is_closing = True
        self.hide()  # Hide window immediately for better visual feedback
        
        # Get Application instance and initiate shutdown
        from ai_twitch_cohost.core.application import Application
        try:
            app = self.service_container.resolve(Application)
            if hasattr(app, 'shutdown'):
                # Create timer to defer shutdown until after event processing
                timer = QTimer(self)
                timer.setSingleShot(True)
                timer.timeout.connect(lambda: asyncio.create_task(app.shutdown()))
                timer.start(100)  # 100ms delay
                
                # Set shutdown flag immediately
                app.request_shutdown()
            else:
                self.logger.warning("Application instance has no shutdown method")
                self.close()
        except Exception as e:
            self.logger.error(f"Error triggering shutdown: {e}", exc_info=True)
            self.close()

    def show_info_message(self, title: str, message: str) -> None:
        """
        Show an info message bar.

        Args:
            title: Message title
            message: Message content
        """
        # Import InfoBar components when needed
        from ai_twitch_cohost.gui.compatibility import InfoBar, InfoBarPosition
        if InfoBar and InfoBarPosition:
            InfoBar.success(
                title=title,
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        else:
            # Fallback: just log the message
            self.logger.info(f"Info: {title} - {message}")

    def show_error_message(self, title: str, message: str) -> None:
        """
        Show an error message bar.

        Args:
            title: Error title
            message: Error message
        """
        # Import InfoBar components when needed
        from ai_twitch_cohost.gui.compatibility import InfoBar, InfoBarPosition
        if InfoBar and InfoBarPosition:
            InfoBar.error(
                title=title,
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self
            )
        else:
            # Fallback: just log the error
            self.logger.error(f"Error: {title} - {message}")

    def closeEvent(self, event: QCloseEvent) -> None:
        """Handle window close event."""
        try:
            settings = self.config_manager.get_user_settings()

            # If minimize to tray is enabled AND we're not forcing close AND Alt is not pressed
            from PyQt6.QtGui import QGuiApplication
            keyboard_modifiers = QGuiApplication.keyboardModifiers()
            force_close = self._is_closing or bool(keyboard_modifiers & Qt.KeyboardModifier.AltModifier)
            
            if settings.ui.minimize_to_tray and not force_close:
                event.ignore()
                self.hide()

                # Show tray message
                if self.system_tray and is_system_tray_available():
                    # Import SystemTrayIcon when needed
                    from ai_twitch_cohost.gui.compatibility import SystemTrayIcon
                    if SystemTrayIcon:
                        self.system_tray.showMessage(
                            APP_NAME,
                            "Application was minimized to tray",
                            SystemTrayIcon.MessageIcon.Information,
                            2000
                        )
                return

            # Save window state
            self._save_window_state()

            # Emit closing signal
            self.closing.emit()

            # Hide window immediately for better visual feedback
            self.hide()
            
            # Get Application instance and initiate shutdown
            from ai_twitch_cohost.core.application import Application
            try:
                app = self.service_container.resolve(Application)
                if hasattr(app, 'shutdown'):
                    # Create timer to defer shutdown until after event processing
                    timer = QTimer(self)
                    timer.setSingleShot(True)
                    timer.timeout.connect(lambda: asyncio.create_task(app.shutdown()))
                    timer.start(100)  # 100ms delay
                    
                    # Set shutdown flag
                    app.request_shutdown()
                    
                    # Ignore close event - shutdown sequence will handle cleanup
                    event.ignore()
                    return
            except Exception as e:
                self.logger.error(f"Error initiating shutdown: {e}", exc_info=True)
            
            # If we get here, something went wrong - accept the close event
            self.logger.warning("Falling back to direct window close")
            event.accept()

        except Exception as e:
            self.logger.error(f"Error during window close: {e}")
            event.accept()

    def _on_tray_activated(self, reason) -> None:
        """Handle system tray activation."""
        # Import SystemTrayIcon when needed
        from ai_twitch_cohost.gui.compatibility import SystemTrayIcon
        if is_system_tray_available() and SystemTrayIcon and reason == SystemTrayIcon.ActivationReason.DoubleClick:
            if self.isVisible():
                self.hide()
            else:
                self.show_window()

    async def _on_config_changed(self) -> None:
        """Handle configuration changes."""
        try:
            # Reload window state if needed
            settings = self.config_manager.get_user_settings()

            # Update always on top setting
            current_on_top = bool(self.windowFlags() & Qt.WindowType.WindowStaysOnTopHint)
            if settings.ui.always_on_top != current_on_top:
                self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint, settings.ui.always_on_top)
                self.show()  # Required to apply flag changes

        except Exception as e:
            self.logger.error(f"Error handling config change in main window: {e}")

    def cleanup(self) -> None:
        """Clean up window resources."""
        # Unregister configuration callback
        self.config_manager.unregister_change_callback(self._on_config_changed)

        # Hide system tray
        if self.system_tray:
            self.system_tray.hide()
            self.system_tray = None
