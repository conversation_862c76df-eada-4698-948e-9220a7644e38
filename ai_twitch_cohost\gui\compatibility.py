"""
GUI compatibility layer for handling qfluentwidgets import issues.

This module provides a compatibility layer that handles the proper initialization
order for qfluentwidgets components to avoid widget creation before QApplication.
"""

import logging
from typing import Optional, Any
from PyQt6.QtWidgets import QMainWindow, QWidget, QTabWidget, QVBoxLayout
from PyQt6.QtCore import pyqtSignal
from PyQt6.QtGui import QIcon

logger = logging.getLogger(__name__)

# Global flags for component availability
FLUENT_WIDGETS_AVAILABLE = False
SYSTEM_TRAY_AVAILABLE = False

def ensure_valid_icon(icon: Any) -> QIcon:
    """
    Convert any icon type to a valid QIcon.
    
    Args:
        icon: Any icon type (QIcon, FluentIcon, etc.)
        
    Returns:
        QIcon: A valid QIcon instance, even if empty
    """
    try:
        if isinstance(icon, QIcon):
            return icon if not icon.isNull() else QIcon()
        elif hasattr(icon, 'icon'):
            # Handle FluentIcon objects
            qicon = icon.icon()
            if isinstance(qicon, QIcon) and not qicon.isNull():
                return qicon
    except Exception as e:
        logger.debug(f"Error converting icon: {e}")
    return QIcon()

# Component references (will be set after import)
FluentWindow = None
NavigationItemPosition = None
FluentIcon = None
InfoBar = None
InfoBarPosition = None
Action = None
SystemTrayIcon = None


def initialize_fluent_widgets() -> bool:
    """
    Initialize qfluentwidgets components after QApplication is ready.

    Returns:
        bool: True if initialization was successful, False otherwise
    """
    global FLUENT_WIDGETS_AVAILABLE, SYSTEM_TRAY_AVAILABLE
    global FluentWindow, NavigationItemPosition, FluentIcon
    global InfoBar, InfoBarPosition, Action, SystemTrayIcon

    # Check if QApplication is available before importing
    try:
        from PyQt6.QtWidgets import QApplication
        if not QApplication.instance():
            logger.warning("QApplication not available, skipping FluentWidgets initialization")
            return False
    except ImportError:
        logger.warning("PyQt6 not available, skipping FluentWidgets initialization")
        return False

    try:
        logger.debug("Importing FluentWidgets components...")

        # Import components one by one to identify which one causes the issue
        logger.debug("Importing FluentWindow...")
        from qfluentwidgets import FluentWindow as _FluentWindow

        logger.debug("Importing NavigationItemPosition...")
        from qfluentwidgets import NavigationItemPosition as _NavigationItemPosition

        logger.debug("Importing FluentIcon...")
        from qfluentwidgets import FluentIcon as _FluentIcon

        logger.debug("Importing InfoBar...")
        from qfluentwidgets import InfoBar as _InfoBar

        logger.debug("Importing InfoBarPosition...")
        from qfluentwidgets import InfoBarPosition as _InfoBarPosition

        logger.debug("Importing Action...")
        from qfluentwidgets import Action as _Action

        FluentWindow = _FluentWindow
        NavigationItemPosition = _NavigationItemPosition
        FluentIcon = _FluentIcon
        InfoBar = _InfoBar
        InfoBarPosition = _InfoBarPosition
        Action = _Action

        FLUENT_WIDGETS_AVAILABLE = True
        logger.debug("FluentWidgets components imported successfully")

        # Apply monkeypatch for NavigationWidget
        try:
            from qfluentwidgets.components.navigation.navigation_widget import NavigationWidget as BaseNavigationWidget
            from PyQt6.QtGui import QIcon, QPaintEvent
            from PyQt6.QtCore import QRect

            if BaseNavigationWidget:
                # Create a safer subclass of NavigationWidget
                class SafeNavigationWidget(BaseNavigationWidget):
                    """NavigationWidget subclass with safer icon handling"""
                    def __init__(self, text: str, icon: Any = None, *args, **kwargs):
                        """Initialize with guaranteed valid icon"""
                        self._stored_icon = ensure_valid_icon(icon)
                        super().__init__(text, self._stored_icon, *args, **kwargs)
                        logger.debug(f"SafeNavigationWidget initialized with text: {text}")

                    def icon(self) -> QIcon:
                        """Always return a valid icon"""
                        if not hasattr(self, '_stored_icon'):
                            self._stored_icon = QIcon()
                        return self._stored_icon

                    def setIcon(self, icon: Any) -> None:
                        """Safely set a new icon"""
                        self._stored_icon = ensure_valid_icon(icon)

                    def paintEvent(self, event: QPaintEvent) -> None:
                        """Safe paint event that ensures valid icon state"""
                        try:
                            super().paintEvent(event)
                        except Exception as e:
                            logger.debug(f"Error in SafeNavigationWidget.paintEvent: {e}")
                            BaseNavigationWidget.paintEvent(self, event)

                # Replace the original NavigationWidget with our safer version
                import qfluentwidgets.components.navigation.navigation_widget as nav_module
                nav_module.NavigationWidget = SafeNavigationWidget
                logger.info("Replaced NavigationWidget with safer implementation")
            else:
                logger.warning("NavigationWidget not found, skipping monkeypatch.")
        except ImportError:
            logger.warning("Failed to import NavigationWidget or QIcon, skipping monkeypatch for qfluentwidgets navigation bug.")
        except Exception as e:
            logger.error(f"Error applying monkeypatch for qfluentwidgets navigation bug: {e}")

        # Try to import SystemTrayIcon
        try:
            from qfluentwidgets import SystemTrayIcon as _SystemTrayIcon
            SystemTrayIcon = _SystemTrayIcon
            SYSTEM_TRAY_AVAILABLE = True
            logger.debug("SystemTrayIcon imported successfully")
        except ImportError:
            SystemTrayIcon = None
            SYSTEM_TRAY_AVAILABLE = False
            logger.warning("SystemTrayIcon not available")

        return True

    except ImportError as e:
        logger.error(f"Failed to import qfluentwidgets: {e}")
        FLUENT_WIDGETS_AVAILABLE = False
        SYSTEM_TRAY_AVAILABLE = False
        return False
    except Exception as e:
        logger.error(f"Error during FluentWidgets initialization: {e}")
        FLUENT_WIDGETS_AVAILABLE = False
        SYSTEM_TRAY_AVAILABLE = False
        return False


class CompatibleMainWindow(QMainWindow):
    """
    A main window that can work with or without FluentWidgets.

    This class provides a fallback to QMainWindow when FluentWindow is not available.
    """

    # Signals
    closing = pyqtSignal()

    def __init__(self, parent: Optional[QWidget] = None):
        """Initialize the compatible main window."""
        # Initialize QMainWindow first
        super().__init__(parent)
        self._fluent_window = None
        self._is_fluent = False

        # Fallback navigation system
        self._tab_widget = None
        self._fallback_initialized = False

        # Don't try to initialize FluentWidgets in __init__ to avoid widget creation issues
        # This will be done later when needed
        logger.debug("CompatibleMainWindow initialized in fallback mode")

    def addSubInterface(self, widget: QWidget, icon: Any, text: str, position: Any = None) -> None:
        """
        Add a sub-interface (tab) to the window.

        Args:
            widget: The widget to add
            icon: Icon for the tab
            text: Text for the tab
            position: Position for the tab (ignored in fallback mode)
        """
        # Try to initialize FluentWidgets on first use
        if not self._is_fluent and not self._fluent_window:
            self._try_initialize_fluent()

        # Create FluentWindow if needed
        if self._is_fluent and not self._fluent_window:
            self._create_fluent_window()

        if self._is_fluent and self._fluent_window:
            try:
                # Use validated icon for interface
                safe_icon = ensure_valid_icon(icon)
                self._fluent_window.addSubInterface(widget, safe_icon, text, position)
                logger.debug(f"Successfully added FluentWindow interface: {text}")
                return

            except Exception as e:
                logger.error(f"Failed to add sub-interface to FluentWindow: {e}")
                logger.info("Falling back to QTabWidget navigation")
                self._is_fluent = False  # Fall back to QMainWindow mode
                # Hide the FluentWindow if it exists
                if self._fluent_window:
                    try:
                        self._fluent_window.hide()
                    except:
                        pass
                    self._fluent_window = None

        # Fallback: create a tabbed interface using QTabWidget
        self._initialize_fallback_navigation()
        self._tab_widget.addTab(widget, text)
        logger.debug(f"Added tab '{text}' to fallback navigation")

    def _initialize_fallback_navigation(self) -> None:
        """Initialize the fallback navigation system using QTabWidget."""
        if self._fallback_initialized:
            return

        # Create tab widget
        self._tab_widget = QTabWidget()
        self._tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self._tab_widget.setMovable(False)

        # Set as central widget
        self.setCentralWidget(self._tab_widget)

        self._fallback_initialized = True
        logger.debug("Fallback navigation initialized with QTabWidget")

    def _try_initialize_fluent(self) -> None:
        """Try to initialize FluentWidgets components."""
        try:
            if initialize_fluent_widgets() and FluentWindow:
                # Just mark as available, don't create FluentWindow yet
                # It will be created when first interface is added
                self._is_fluent = True
                logger.debug("FluentWidgets available, will create FluentWindow when needed")
            else:
                logger.info("FluentWidgets not available, using QTabWidget fallback")
                self._is_fluent = False
        except Exception as e:
            logger.warning(f"Failed to initialize FluentWidgets: {e}")
            self._is_fluent = False

    def _create_fluent_window(self) -> None:
        """Create the FluentWindow instance when needed."""
        if self._fluent_window or not self._is_fluent:
            return

        # Check if QApplication is available
        from PyQt6.QtWidgets import QApplication
        if not QApplication.instance():
            logger.error("QApplication not available, cannot create FluentWindow")
            self._is_fluent = False
            return

        try:
            logger.debug("Creating FluentWindow instance...")
            # Create FluentWindow instance
            self._fluent_window = FluentWindow()

            # Transfer window properties from QMainWindow to FluentWindow
            self._transfer_window_properties()

            logger.debug("FluentWindow created successfully")
        except Exception as e:
            logger.error(f"Failed to create FluentWindow: {e}")
            self._is_fluent = False
            self._fluent_window = None

    def _transfer_window_properties(self) -> None:
        """Transfer window properties from QMainWindow to FluentWindow."""
        if not self._fluent_window:
            return

        # Transfer basic properties
        self._fluent_window.setWindowTitle(self.windowTitle())
        self._fluent_window.resize(self.size())
        self._fluent_window.move(self.pos())

        # Transfer window flags and state
        self._fluent_window.setWindowFlags(self.windowFlags())
        if self.isMaximized():
            self._fluent_window.showMaximized()
        elif self.isMinimized():
            self._fluent_window.showMinimized()

        logger.debug("Window properties transferred to FluentWindow")

    def show(self) -> None:
        """Show the window."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.show()
        else:
            super().show()

    def hide(self) -> None:
        """Hide the window."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.hide()
        else:
            super().hide()

    def closeEvent(self, event) -> None:
        """Handle window close event."""
        # Emit closing signal before closing
        self.closing.emit()
        
        if self._is_fluent and self._fluent_window:
            # Hide fluent window first to prevent visual artifacts
            self._fluent_window.hide()
            self._fluent_window.close()
            self._fluent_window = None
        
        # Ensure proper cleanup
        self._is_fluent = False
        if self._tab_widget:
            self._tab_widget.clear()
            self._tab_widget = None
        
        # Accept the close event
        event.accept()
        super().closeEvent(event)
        
        # Check if we should quit the application
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            # Get all top-level windows
            windows = app.topLevelWindows()
            # If this was the last visible window, quit the application
            visible_windows = [w for w in windows if w.isVisible()]
            if not visible_windows:
                logger.debug("No visible windows remaining, quitting application")
                app.quit()

    def close(self) -> bool:
        """Close the window."""
        # Emit closing signal
        self.closing.emit()
        
        if self._is_fluent and self._fluent_window:
            self._fluent_window.hide()
            result = self._fluent_window.close()
            self._fluent_window = None
            self._is_fluent = False
            return result
        else:
            return super().close()

    def setWindowTitle(self, title: str) -> None:
        """Set the window title."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.setWindowTitle(title)
        else:
            super().setWindowTitle(title)

    def resize(self, width: int, height: int) -> None:
        """Resize the window."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.resize(width, height)
        else:
            super().resize(width, height)

    def setMinimumSize(self, width: int, height: int) -> None:
        """Set minimum window size."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.setMinimumSize(width, height)
        else:
            super().setMinimumSize(width, height)

    def move(self, x: int, y: int) -> None:
        """Move the window."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.move(x, y)
        else:
            super().move(x, y)

    def isVisible(self) -> bool:
        """Check if window is visible."""
        if self._is_fluent and self._fluent_window:
            return self._fluent_window.isVisible()
        else:
            return super().isVisible()

    def isMaximized(self) -> bool:
        """Check if window is maximized."""
        if self._is_fluent and self._fluent_window:
            return self._fluent_window.isMaximized()
        else:
            return super().isMaximized()

    def showMaximized(self) -> None:
        """Show window maximized."""
        if self._is_fluent and self._fluent_window:
            self._fluent_window.showMaximized()
        else:
            super().showMaximized()


def is_fluent_widgets_available() -> bool:
    """Check if FluentWidgets is available."""
    return FLUENT_WIDGETS_AVAILABLE


def is_system_tray_available() -> bool:
    """Check if SystemTrayIcon is available."""
    return SYSTEM_TRAY_AVAILABLE


def get_fluent_icon(name: str) -> Any:
    """
    Get a FluentIcon by name, with fallback.

    Args:
        name: Icon name (e.g., 'HOME', 'CHAT', 'SETTING')

    Returns:
        FluentIcon if available, QIcon if FluentIcon not valid, or None if not available
    """
    if FLUENT_WIDGETS_AVAILABLE and FluentIcon:
        # First try the requested icon
        icon = getattr(FluentIcon, name, None)
        if icon is not None:
            qicon = ensure_valid_icon(icon)
            if not qicon.isNull():
                return icon

        # Try fallback icons if requested one is invalid
        for fallback in ['FOLDER', 'HOME', 'APP']:
            icon = getattr(FluentIcon, fallback, None)
            if icon is not None:
                qicon = ensure_valid_icon(icon)
                if not qicon.isNull():
                    logger.warning(f"FluentIcon.{name} invalid, using {fallback} as fallback")
                    return icon

        # If all FluentIcon attempts fail, return empty QIcon
        logger.warning(f"All FluentIcon attempts failed for {name}, using empty QIcon")
        return QIcon()

    # If FluentWidgets not available or error occurred, return None
    # (will be handled by fallback navigation)
    return None


def get_navigation_position(name: str) -> Any:
    """
    Get a NavigationItemPosition by name, with fallback.

    Args:
        name: Position name (e.g., 'TOP', 'BOTTOM')

    Returns:
        NavigationItemPosition if available, fallback position otherwise
    """
    if FLUENT_WIDGETS_AVAILABLE and NavigationItemPosition:
        position = getattr(NavigationItemPosition, name, None)
        if position is not None:
            return position
        # Fallback to TOP if the specific position doesn't exist
        logger.warning(f"NavigationItemPosition.{name} not found, using TOP as fallback")
        return getattr(NavigationItemPosition, 'TOP', None)

    # If FluentWidgets not available, return None (will be handled by fallback navigation)
    return None
