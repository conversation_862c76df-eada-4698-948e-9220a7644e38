# Contributing to AI Twitch Co-Host

Thank you for your interest in contributing to the AI Twitch Co-Host project! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Python 3.11 or higher
- Git
- Basic knowledge of PyQt6 and Python async programming
- Familiarity with AI APIs (OpenRouter, DeepSeek, Ollama) is helpful but not required

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/ai-twitch-cohost.git
   cd ai-twitch-cohost
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Run Tests**
   ```bash
   pytest
   ```

5. **Start Application**
   ```bash
   python run.py
   ```

## 📋 How to Contribute

### Reporting Bugs

1. **Check existing issues** to avoid duplicates
2. **Use the bug report template** when creating new issues
3. **Include detailed information:**
   - OS and Python version
   - Steps to reproduce
   - Expected vs actual behavior
   - Error messages and stack traces
   - Screenshots if applicable

### Suggesting Features

1. **Check the roadmap** in `TODO.md` to see if it's already planned
2. **Create a feature request** using the template
3. **Describe the use case** and why it would be valuable
4. **Consider implementation complexity** and maintenance burden

### Contributing Code

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the coding standards below
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   pytest
   black .
   flake8
   mypy ai_twitch_cohost/
   ```

4. **Commit your changes**
   ```bash
   git commit -m "feat(scope): add your feature description"
   ```

5. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Coding Standards

### Python Style

- **Follow PEP 8** with line length of 88 characters (Black default)
- **Use type hints** for all function parameters and return values
- **Write docstrings** for all classes, methods, and functions
- **Use meaningful variable names** and avoid abbreviations

### Code Quality Tools

- **Black** for code formatting
- **Flake8** for linting
- **MyPy** for type checking
- **Pytest** for testing

### Example Code Style

```python
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ExampleService:
    """Example service demonstrating coding standards.
    
    This class shows the expected code style including type hints,
    docstrings, and error handling.
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """Initialize the service with configuration.
        
        Args:
            config: Configuration dictionary containing service settings
            
        Raises:
            ValueError: If required configuration is missing
        """
        self.config = config
        self._validate_config()
    
    def process_data(self, data: str, timeout: Optional[float] = None) -> Dict[str, Any]:
        """Process input data and return results.
        
        Args:
            data: Input data to process
            timeout: Optional timeout in seconds
            
        Returns:
            Dictionary containing processed results
            
        Raises:
            ProcessingError: If data processing fails
        """
        try:
            # Implementation here
            result = {"processed": True, "data": data}
            logger.info(f"Successfully processed data: {len(data)} characters")
            return result
        except Exception as e:
            logger.error(f"Failed to process data: {e}")
            raise ProcessingError(f"Processing failed: {e}") from e
    
    def _validate_config(self) -> None:
        """Validate the configuration (private method)."""
        required_keys = ["api_key", "endpoint"]
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"Missing required config key: {key}")
```

### Testing Standards

- **Write tests for all new functionality**
- **Aim for 80%+ code coverage**
- **Use descriptive test names**
- **Test both success and failure cases**
- **Mock external dependencies**

```python
import pytest
from unittest.mock import Mock, patch
from ai_twitch_cohost.services.example import ExampleService

class TestExampleService:
    """Test suite for ExampleService."""
    
    def test_process_data_success(self):
        """Test successful data processing."""
        config = {"api_key": "test", "endpoint": "http://test.com"}
        service = ExampleService(config)
        
        result = service.process_data("test data")
        
        assert result["processed"] is True
        assert result["data"] == "test data"
    
    def test_process_data_with_invalid_input_raises_error(self):
        """Test that invalid input raises appropriate error."""
        config = {"api_key": "test", "endpoint": "http://test.com"}
        service = ExampleService(config)
        
        with pytest.raises(ProcessingError):
            service.process_data("")
    
    @patch('ai_twitch_cohost.services.example.external_api_call')
    def test_process_data_with_api_failure(self, mock_api):
        """Test handling of external API failures."""
        mock_api.side_effect = ConnectionError("API unavailable")
        config = {"api_key": "test", "endpoint": "http://test.com"}
        service = ExampleService(config)
        
        with pytest.raises(ProcessingError):
            service.process_data("test data")
```

## 🏗️ Architecture Guidelines

### Plugin Development

- **Inherit from base plugin classes**
- **Use dependency injection** for services
- **Implement proper error handling**
- **Follow the plugin lifecycle** (initialize, start, stop, cleanup)

### Event System

- **Use the event bus** for inter-component communication
- **Define clear event schemas**
- **Handle events asynchronously** when possible
- **Avoid tight coupling** between components

### Configuration

- **Use Pydantic models** for configuration validation
- **Support environment variables** for sensitive data
- **Provide sensible defaults**
- **Document all configuration options**

## 🔍 Review Process

### Pull Request Guidelines

1. **Fill out the PR template** completely
2. **Link related issues** using keywords (fixes #123)
3. **Provide clear description** of changes
4. **Include screenshots** for UI changes
5. **Ensure all tests pass**
6. **Request review** from maintainers

### Review Criteria

- **Code quality** and adherence to standards
- **Test coverage** for new functionality
- **Documentation** updates
- **Performance impact** consideration
- **Security implications** review
- **Backward compatibility** preservation

## 🎯 Priority Areas

We especially welcome contributions in these areas:

1. **Plugin Development**
   - Chat games and interactive features
   - Stream alerts and notifications
   - Custom command systems

2. **AI Integration**
   - New AI service providers
   - Improved prompt engineering
   - Context management optimization

3. **Audio Processing**
   - Additional TTS/STT providers
   - Audio effects and filters
   - Voice activity detection

4. **Testing & Quality**
   - Expand test coverage
   - Performance testing
   - Cross-platform compatibility

5. **Documentation**
   - User guides and tutorials
   - API documentation
   - Plugin development guides

## 📞 Getting Help

- **GitHub Issues** - For bugs and feature requests
- **GitHub Discussions** - For questions and general discussion
- **Discord** - Real-time chat (link in README)
- **Email** - For security issues or private matters

## 📜 Code of Conduct

This project follows the [Contributor Covenant Code of Conduct](https://www.contributor-covenant.org/version/2/1/code_of_conduct/). By participating, you are expected to uphold this code.

## 🏆 Recognition

Contributors will be recognized in:
- **README.md** contributors section
- **Release notes** for significant contributions
- **GitHub contributors** page
- **Special thanks** in documentation

Thank you for contributing to AI Twitch Co-Host! 🚀
