"""
Main application class that orchestrates all components and services.

This module contains the central Application class that manages the lifecycle
of all application components, handles initialization and shutdown, and
coordinates between different subsystems.
"""

import asyncio
import sys
from typing import Optional

# Optional PyQt6 imports
try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QTimer
    PYQT6_AVAILABLE = True
except ImportError:
    QApplication = None
    QTimer = None
    PYQT6_AVAILABLE = False

from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.service_container import ServiceContainer
from ai_twitch_cohost.core.event_bus import EventBus
from ai_twitch_cohost.core.logging_config import LoggerMixin
from ai_twitch_cohost import APP_NAME, APP_VERSION


class Application(LoggerMixin):
    """
    Main application class that orchestrates all components and services.

    This class is responsible for:
    - Initializing and configuring all application services
    - Managing the application lifecycle
    - Coordinating between GUI and background services
    - Handling graceful shutdown
    """

    def __init__(self, config_manager: ConfigManager):
        """
        Initialize the application.

        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.service_container = ServiceContainer()
        self.event_bus = EventBus()

        # Qt Application
        self.qt_app = None  # Optional[QApplication] when available
        self.main_window = None

        # Application state
        self._running = False
        self._shutdown_requested = False
        self._shutdown_lock = asyncio.Lock()

        # Background tasks
        self._background_tasks: set = set()

        # Setup signal handlers
        self._setup_signal_handlers()
        
        self.logger.info(f"Application initialized - {APP_NAME} v{APP_VERSION}")

    def _setup_signal_handlers(self) -> None:
        """Setup handlers for system signals."""
        import signal
        
        def signal_handler(signum, frame):
            signame = signal.Signals(signum).name
            self.logger.info(f"Received signal {signame}, initiating graceful shutdown...")
            
            # Use asyncio to schedule shutdown
            if self._running:
                asyncio.get_event_loop().create_task(self.shutdown())
            else:
                self.request_shutdown()
        
        try:
            # Register signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # Optional: handle SIGBREAK on Windows
            if hasattr(signal, 'SIGBREAK'):
                signal.signal(signal.SIGBREAK, signal_handler)
                
        except Exception as e:
            self.logger.warning(f"Could not set up signal handlers: {e}", exc_info=True)

    async def start(self) -> None:
        """Start the application and all its services."""
        try:
            self.logger.info("Starting application...")

            # Register core services
            await self._register_core_services()

            # Initialize Qt application
            await self._initialize_qt_application()

            # Initialize services
            await self._initialize_services()

            # Start background services
            await self._start_background_services()

            # Show main window
            await self._show_main_window()

            self._running = True
            self.logger.info("Application started successfully")

        except Exception as e:
            self.logger.exception(f"Failed to start application: {e}")
            await self.shutdown()
            raise

    async def run(self) -> None:
        """Run the application main loop."""
        if not self._running:
            raise RuntimeError("Application not started")

        self.logger.info("Application main loop started")

        try:
            if PYQT6_AVAILABLE and self.qt_app:
                # GUI mode - run Qt event loop
                # Create a timer to process asyncio events in Qt event loop
                timer = QTimer()
                timer.timeout.connect(lambda: None)  # Just to keep the event loop active
                timer.start(10)  # 10ms interval

                # Run until shutdown is requested
                while not self._shutdown_requested:
                    try:
                        # Process Qt events
                        if self.qt_app:  # Check if Qt app still exists
                            self.qt_app.processEvents()
                            
                            # Check if window was closed
                            if not self.main_window or not self.main_window.isVisible():
                                self.logger.debug("Main window closed, initiating shutdown")
                                self.request_shutdown()
                                break

                        # Process asyncio events with timeout
                        try:
                            await asyncio.wait_for(asyncio.sleep(0.01), timeout=0.1)
                        except asyncio.TimeoutError:
                            # This is expected, just continue
                            pass
                        except Exception as e:
                            self.logger.warning(f"Error in asyncio event loop: {e}", exc_info=True)

                        # Check if Qt application wants to quit
                        if not self.qt_app:
                            self.logger.info("Qt application terminated, initiating shutdown")
                            break
                            
                    except Exception as e:
                        self.logger.error(f"Error in main event loop: {e}", exc_info=True)
                        # Request shutdown on critical errors
                        self.request_shutdown()
            else:
                # Headless mode - just wait for shutdown
                self.logger.info("Running in headless mode")
                while not self._shutdown_requested:
                    await asyncio.sleep(1.0)

        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            self.logger.exception(f"Error in application main loop: {e}")
        finally:
            await self.shutdown()

    async def shutdown(self) -> None:
        """Shutdown the application gracefully."""
        # Use lock to prevent multiple simultaneous shutdown attempts
        async with self._shutdown_lock:
            if self._shutdown_requested:
                self.logger.debug("Shutdown already in progress")
                return

            self._shutdown_requested = True
            self.logger.info("Shutting down application...")

            try:
                # Cancel background tasks
                await self._cancel_background_tasks()

                # Close main window first to ensure proper cleanup
                if self.main_window:
                    self.main_window.close()
                    self.main_window = None

                # Wait a brief moment for Qt events to process
                await asyncio.sleep(0.1)

                # Shutdown services before quitting Qt
                await self._shutdown_services()

                # Remove self from service container to prevent circular disposal
                if self.service_container.is_registered(Application):
                    self._unregister_self()

                # Dispose service container
                self.service_container.dispose()

                # Shutdown configuration manager
                await self.config_manager.shutdown()

                # Quit Qt application last, after all services are shut down
                if self.qt_app:
                    self.qt_app.quit()
                    self.qt_app = None

                self._running = False
                self.logger.info("Application shutdown complete")

            except Exception as e:
                self.logger.error(f"Error during application shutdown: {e}", exc_info=True)
            finally:
                # Always release the lock, even if an error occurs
                self.logger.debug("Shutdown sequence completed")

    async def _register_core_services(self) -> None:
        """Register core application services."""
        self.logger.debug("Registering core services...")

        # Register core services as singletons
        self.service_container.register_singleton(ConfigManager, instance=self.config_manager)
        self.service_container.register_singleton(EventBus, instance=self.event_bus)
        self.service_container.register_singleton(ServiceContainer, instance=self.service_container)

        # Register application instance
        self.service_container.register_singleton(Application, instance=self)

        self.logger.debug("Core services registered")

    async def _initialize_qt_application(self) -> None:
        """Initialize the Qt application."""
        if not PYQT6_AVAILABLE:
            self.logger.warning("PyQt6 not available, running in headless mode")
            return

        self.logger.debug("Initializing Qt application...")

        try:
            # Set high DPI scaling policy before creating QApplication
            if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
                from PyQt6.QtCore import Qt
                QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

            # Create Qt application if it doesn't exist
            if not QApplication.instance():
                self.qt_app = QApplication(sys.argv)
            else:
                self.qt_app = QApplication.instance()

            # Set application properties
            self.qt_app.setApplicationName(APP_NAME)
            self.qt_app.setApplicationVersion(APP_VERSION)
            self.qt_app.setOrganizationName("AI Twitch Co-Host")

            # Prevent the application from quitting when last window is closed
            # We want to handle this ourselves via the shutdown sequence
            self.qt_app.setQuitOnLastWindowClosed(False)

            # Connect Qt's quit signals to our shutdown procedure
            def last_window_closed_handler():
                self.logger.debug("Last window closed signal received")
                if self.main_window:
                    self.main_window.hide()
                self.request_shutdown()
                
            def about_to_quit_handler():
                self.logger.debug("About to quit signal received")
                if self.main_window:
                    self.main_window.hide()
                self.request_shutdown()

            # Connect handlers directly to avoid asyncio issues
            self.qt_app.lastWindowClosed.connect(last_window_closed_handler)
            if hasattr(self.qt_app, 'aboutToQuit'):
                self.qt_app.aboutToQuit.connect(about_to_quit_handler)

            self.logger.debug("Qt application initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize Qt application: {e}")
            # Fall back to headless mode
            self.qt_app = None

    async def _apply_application_theme(self) -> None:
        """Apply the application theme and styling."""
        if not PYQT6_AVAILABLE or not self.qt_app:
            return

        try:
            from ai_twitch_cohost.gui.theme_manager import ThemeManager

            # Get theme manager and apply theme
            theme_manager = ThemeManager(self.config_manager)
            await theme_manager.apply_theme()

            self.logger.debug("Application theme applied")

        except ImportError:
            self.logger.warning("Theme manager not available, using default theme")
        except Exception as e:
            self.logger.error(f"Failed to apply application theme: {e}")

    async def _initialize_services(self) -> None:
        """Initialize all application services."""
        self.logger.debug("Initializing services...")

        # Import and register service modules
        try:
            # AI Services
            from ai_twitch_cohost.services.ai import ai_service_module
            await self._register_service_module(ai_service_module)

            # Audio Services
            from ai_twitch_cohost.services.audio import audio_service_module
            await self._register_service_module(audio_service_module)

            # Twitch Services
            from ai_twitch_cohost.services.twitch import twitch_service_module
            await self._register_service_module(twitch_service_module)

            # Plugin Services
            from ai_twitch_cohost.services.plugins import plugin_service_module
            await self._register_service_module(plugin_service_module)

        except ImportError as e:
            self.logger.warning(f"Some services not available: {e}")

        self.logger.debug("Services initialized")

    async def _register_service_module(self, module) -> None:
        """Register services from a module."""
        if hasattr(module, 'register_services'):
            await module.register_services(self.service_container)

    async def _start_background_services(self) -> None:
        """Start background services and tasks."""
        self.logger.debug("Starting background services...")

        # Start services that need background processing
        try:
            # Start AI service
            if self.service_container.is_registered('AIService'):
                ai_service = self.service_container.resolve('AIService')
                if hasattr(ai_service, 'start'):
                    task = asyncio.create_task(ai_service.start())
                    self._background_tasks.add(task)

            # Start Twitch service
            if self.service_container.is_registered('TwitchService'):
                twitch_service = self.service_container.resolve('TwitchService')
                if hasattr(twitch_service, 'start'):
                    task = asyncio.create_task(twitch_service.start())
                    self._background_tasks.add(task)

            # Start plugin manager
            if self.service_container.is_registered('PluginManager'):
                plugin_manager = self.service_container.resolve('PluginManager')
                if hasattr(plugin_manager, 'start'):
                    task = asyncio.create_task(plugin_manager.start())
                    self._background_tasks.add(task)

        except Exception as e:
            self.logger.error(f"Error starting background services: {e}")

        self.logger.debug("Background services started")

    async def _show_main_window(self) -> None:
        """Create and show the main application window."""
        if not PYQT6_AVAILABLE:
            self.logger.info("GUI not available, skipping main window creation")
            return

        self.logger.debug("Creating main window...")

        try:
            from ai_twitch_cohost.gui.main_window import MainWindow

            # Create main window
            self.main_window = MainWindow(
                config_manager=self.config_manager,
                service_container=self.service_container,
                event_bus=self.event_bus
            )

            # Apply theme after main window is created
            await self._apply_application_theme()

            # Show the window
            self.main_window.show()

            self.logger.debug("Main window created and shown")

        except ImportError:
            self.logger.warning("GUI components not available, running in headless mode")
            # Fall back to headless mode
            self.qt_app = None
        except Exception as e:
            self.logger.error(f"Failed to create main window: {e}")
            # Fall back to headless mode
            self.qt_app = None

    async def _cancel_background_tasks(self) -> None:
        """Cancel all background tasks."""
        if not self._background_tasks:
            return

        self.logger.debug("Cancelling background tasks...")

        # Cancel all tasks
        for task in self._background_tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)

        self._background_tasks.clear()
        self.logger.debug("Background tasks cancelled")

    async def _shutdown_services(self) -> None:
        """Shutdown all application services."""
        self.logger.debug("Shutting down services...")

        # Shutdown services in reverse order
        service_types = [
            'PluginManager',
            'TwitchService',
            'AudioService',
            'AIService'
        ]

        for service_type in service_types:
            try:
                if self.service_container.is_registered(service_type):
                    service = self.service_container.resolve(service_type)
                    if hasattr(service, 'shutdown'):
                        await service.shutdown()
            except Exception as e:
                self.logger.error(f"Error shutting down {service_type}: {e}")

        self.logger.debug("Services shutdown complete")

    def is_running(self) -> bool:
        """Check if the application is running."""
        return self._running

    def request_shutdown(self) -> None:
        """Request application shutdown."""
        self._shutdown_requested = True

    def _on_last_window_closed(self) -> None:
        """Handle the last window being closed."""
        self.logger.debug("Last window closed, initiating shutdown")
        # Hide window immediately to prevent visual artifacts
        if self.main_window:
            self.main_window.hide()
        # Schedule shutdown
        asyncio.create_task(self.shutdown())

    def _on_about_to_quit(self) -> None:
        """Handle Qt application about to quit."""
        self.logger.debug("Qt application about to quit")
        # Hide window immediately to prevent visual artifacts
        if self.main_window:
            self.main_window.hide()
        # Request shutdown but don't start it (Qt will handle quit)
        self.request_shutdown()

    def _unregister_self(self) -> None:
        """Safely remove the Application instance from the service container."""
        try:
            # Remove Application instance directly from container internals
            if Application in self.service_container._instances:
                del self.service_container._instances[Application]
            if Application in self.service_container._services:
                del self.service_container._services[Application]
            self.logger.debug("Application unregistered from service container")
        except Exception as e:
            self.logger.error(f"Error unregistering Application: {e}", exc_info=True)
