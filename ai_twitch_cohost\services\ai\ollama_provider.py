"""
Ollama local AI provider implementation.

Provides integration with local Ollama instance for model discovery,
management, and local inference monitoring.
"""

import httpx
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.event_bus import EventBus
from ai_twitch_cohost.services.ai.base_ai_provider import BaseAIProvider
from ai_twitch_cohost.services.ai.models import (
    AIRequest, AIResponse, AIError, AIModelInfo, AIProviderType
)
from ai_twitch_cohost.services.ai.rate_limiter import RateLimiter
from ai_twitch_cohost.events.ai_events import AIModelDiscoveredEvent


class OllamaProvider(BaseAIProvider):
    """Ollama local AI provider implementation."""

    def __init__(
        self,
        config_manager: ConfigManager,
        event_bus: EventBus,
        rate_limiter: RateLimiter
    ):
        """Initialize Ollama provider."""
        super().__init__(AIProviderType.OLLAMA, config_manager, event_bus, rate_limiter)
        self._client: Optional[httpx.AsyncClient] = None
        self._config = None

    def _setup_rate_limiting(self) -> None:
        """Setup rate limiting buckets for Ollama."""
        # Local Ollama has no API rate limits, but we limit for resource management
        self.rate_limiter.add_bucket(
            f"{self.name}_requests",
            capacity=10,  # 10 concurrent requests max
            refill_rate=2.0  # 2 requests per second
        )

        # No token rate limiting for local models

    async def initialize(self) -> bool:
        """Initialize the Ollama provider."""
        try:
            self._config = self.config_manager.get_user_settings().ai.ollama

            # Create HTTP client
            self._client = httpx.AsyncClient(
                base_url=self._config.base_url,
                headers={"Content-Type": "application/json"},
                timeout=self._config.timeout_seconds
            )

            # Test connection and discover models
            if await self.check_health():
                await self.discover_models()
                await self.update_status("online")
                self.logger.info("Ollama provider initialized successfully")
                return True
            else:
                await self.update_status("error", "Ollama not available")
                return False

        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama provider: {e}")
            await self.update_status("error", str(e))
            return False

    async def discover_models(self) -> List[AIModelInfo]:
        """Discover available models from Ollama."""
        if not self._client:
            return []

        try:
            response = await self._client.get("/api/tags")
            response.raise_for_status()

            response_data = response.json()
            models = []

            for model_data in response_data.get("models", []):
                # Extract model info
                name = model_data.get("name", "")
                size = model_data.get("size", 0)
                modified_at = model_data.get("modified_at", "")

                # Estimate capabilities based on model name
                capabilities = self._estimate_capabilities(name)

                model_info = AIModelInfo(
                    id=name,
                    name=name,
                    description=f"Local Ollama model ({self._format_size(size)})",
                    context_length=self._estimate_context_length(name),
                    pricing={"prompt": 0.0, "completion": 0.0},  # Local models are free
                    capabilities=capabilities,
                    provider="ollama",
                    available=True
                )
                models.append(model_info)
                self._available_models[model_info.id] = model_info

            self.logger.info(f"Discovered {len(models)} Ollama models")

            # Emit model discovery event
            await self.event_bus.emit(AIModelDiscoveredEvent(
                provider=self.name,
                models={model.id: model.dict() for model in models}
            ))

            return models

        except Exception as e:
            self.logger.error(f"Failed to discover Ollama models: {e}")
            return []

    async def generate_response(self, request: AIRequest) -> AIResponse:
        """Generate a response using Ollama."""
        if not self._client:
            raise AIError(
                error_type="not_initialized",
                message="Ollama provider not initialized",
                provider=self.name
            )

        # Use specified model or default
        model = request.model or self._config.default_model
        if not model:
            raise AIError(
                error_type="no_model",
                message="No model specified and no default model configured",
                provider=self.name
            )

        # Build prompt from conversation history
        prompt = self._build_prompt(request)

        # Prepare request data
        request_data = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": request.temperature or 0.7,
                "num_predict": request.max_tokens or 4096
            }
        }

        try:
            # Make the API call
            response = await self._client.post("/api/generate", json=request_data)
            response.raise_for_status()

            response_data = response.json()

            # Extract response content
            content = response_data.get("response", "")
            if not content:
                raise AIError(
                    error_type="no_response",
                    message="No response content returned",
                    provider=self.name,
                    model=model
                )

            # Extract metrics (Ollama provides some timing info)
            eval_count = response_data.get("eval_count", 0)
            eval_duration = response_data.get("eval_duration", 0)

            return AIResponse(
                content=content,
                model=model,
                provider=self.name,
                tokens_used=eval_count,
                cost=0.0,  # Local models are free
                response_time_ms=0,  # Will be set by base class
                request_id=request.request_id,
                finish_reason="stop",
                metadata={
                    "eval_count": eval_count,
                    "eval_duration": eval_duration,
                    "total_duration": response_data.get("total_duration", 0),
                    "load_duration": response_data.get("load_duration", 0)
                }
            )

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP {e.response.status_code}: {e.response.text}"
            raise AIError(
                error_type="http_error",
                message=error_msg,
                code=str(e.response.status_code),
                provider=self.name,
                model=model,
                retryable=e.response.status_code in [500, 502, 503, 504]
            )
        except Exception as e:
            raise AIError(
                error_type="request_failed",
                message=str(e),
                provider=self.name,
                model=model
            )

    async def check_health(self) -> bool:
        """Check if Ollama is healthy and available."""
        if not self._client:
            return False

        try:
            response = await self._client.get("/api/tags", timeout=5.0)
            return response.status_code == 200
        except Exception as e:
            self.logger.warning(f"Ollama health check failed: {e}")
            return False

    def _calculate_cost(self, model: str, prompt_tokens: int, completion_tokens: int) -> float:
        """Calculate cost for Ollama request (always 0 for local models)."""
        return 0.0

    def _build_prompt(self, request: AIRequest) -> str:
        """Build a prompt from the request and conversation history."""
        parts = []

        # Add system prompt if provided
        if request.system_prompt:
            parts.append(f"System: {request.system_prompt}")

        # Add conversation history
        for msg in request.conversation_history:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role == "user":
                parts.append(f"User: {content}")
            elif role == "assistant":
                parts.append(f"Assistant: {content}")
            elif role == "system":
                parts.append(f"System: {content}")

        # Add current prompt
        parts.append(f"User: {request.prompt}")
        parts.append("Assistant:")

        return "\n\n".join(parts)

    def _estimate_capabilities(self, model_name: str) -> List[str]:
        """Estimate model capabilities based on name."""
        capabilities = ["chat"]

        name_lower = model_name.lower()

        if "code" in name_lower or "coder" in name_lower:
            capabilities.extend(["coding", "debugging"])

        if "instruct" in name_lower:
            capabilities.append("instruction-following")

        if "chat" in name_lower:
            capabilities.append("conversation")

        if any(term in name_lower for term in ["llama", "mistral", "phi", "gemma"]):
            capabilities.extend(["reasoning", "analysis"])

        return capabilities

    def _estimate_context_length(self, model_name: str) -> int:
        """Estimate context length based on model name."""
        name_lower = model_name.lower()

        # Common context lengths for popular models
        if "32k" in name_lower:
            return 32768
        elif "16k" in name_lower:
            return 16384
        elif "8k" in name_lower:
            return 8192
        elif "4k" in name_lower:
            return 4096
        elif any(term in name_lower for term in ["llama2", "mistral"]):
            return 4096
        elif "phi" in name_lower:
            return 2048
        else:
            return 4096  # Default assumption

    def _format_size(self, size_bytes: int) -> str:
        """Format model size in human-readable format."""
        if size_bytes == 0:
            return "Unknown size"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0

        return f"{size_bytes:.1f} TB"

    async def pull_model(self, model_name: str) -> bool:
        """
        Pull/download a model to Ollama.

        Args:
            model_name: Name of the model to pull

        Returns:
            True if successful, False otherwise
        """
        if not self._client:
            return False

        try:
            self.logger.info(f"Pulling Ollama model: {model_name}")

            request_data = {"name": model_name}
            response = await self._client.post("/api/pull", json=request_data)
            response.raise_for_status()

            # Refresh model list
            await self.discover_models()

            self.logger.info(f"Successfully pulled model: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to pull model {model_name}: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self._client:
            await self._client.aclose()
            self._client = None

        await self.update_status("offline")
        self.logger.info("Ollama provider cleaned up")
