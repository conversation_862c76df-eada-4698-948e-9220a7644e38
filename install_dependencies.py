#!/usr/bin/env python3
"""
Dependency installer for AI Twitch Co-Host.

This script handles the proper installation of dependencies, including
PyQt-Fluent-Widgets which requires special handling.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """Run a command and handle errors."""
    print(f"🔄 {description}")
    print(f"   Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"   Error: {e.stderr}")
        return False

def main():
    """Main installation process."""
    print("🚀 AI Twitch Co-Host Dependency Installer")
    print("=" * 50)
    
    # Check if we're in a virtual environment
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Not in a virtual environment!")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("Aborted. Please activate your virtual environment first.")
            return 1
    
    # Get the project root
    project_root = Path(__file__).parent
    requirements_file = project_root / "requirements.txt"
    requirements_dev_file = project_root / "requirements-dev.txt"
    
    # Step 1: Upgrade pip
    if not run_command([
        sys.executable, "-m", "pip", "install", "--upgrade", "pip"
    ], "Upgrading pip"):
        return 1
    
    # Step 2: Install PyQt-Fluent-Widgets with specific index
    if not run_command([
        sys.executable, "-m", "pip", "install", 
        "PyQt-Fluent-Widgets[full]>=1.4.0",
        "-i", "https://pypi.org/simple/"
    ], "Installing PyQt-Fluent-Widgets (full version)"):
        return 1
    
    # Step 3: Install main requirements
    if requirements_file.exists():
        if not run_command([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], "Installing main requirements"):
            return 1
    else:
        print(f"❌ Requirements file not found: {requirements_file}")
        return 1
    
    # Step 4: Install dev requirements (optional)
    if requirements_dev_file.exists():
        response = input("\n📦 Install development dependencies? (y/N): ")
        if response.lower() == 'y':
            if not run_command([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_dev_file)
            ], "Installing development requirements"):
                print("⚠️  Development dependencies failed, but continuing...")
    
    print("\n🎉 Installation completed successfully!")
    print("\nNext steps:")
    print("1. Run: python test_basic_setup.py")
    print("2. Run: python simple_demo.py")
    print("3. Run: python run.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
