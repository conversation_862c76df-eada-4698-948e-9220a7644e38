"""
Dashboard page for AI Twitch Co-Host.

This module provides the main dashboard interface showing application status,
recent activity, and quick access to key features.
"""

import logging
from typing import Optional
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame, QGridLayout, QHBoxLayout
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import <PERSON><PERSON>ont
from qfluentwidgets import ScrollArea

from ai_twitch_cohost.core.config_manager import ConfigManager
from ai_twitch_cohost.core.service_container import ServiceContainer
from ai_twitch_cohost.core.event_bus import EventBus


class DashboardPage(ScrollArea):
    """
    Main dashboard page for the AI Twitch Co-Host application.

    Displays:
    - Application status
    - Service status indicators
    - Recent activity
    - Quick action buttons
    """

    def __init__(
        self,
        config_manager: ConfigManager,
        service_container: ServiceContainer,
        event_bus: EventBus,
        parent: Optional[QWidget] = None
    ):
        """
        Initialize the dashboard page.

        Args:
            config_manager: Configuration manager instance
            service_container: Service container for dependency injection
            event_bus: Event bus for inter-component communication
            parent: Parent widget
        """
        super().__init__(parent)

        self.config_manager = config_manager
        self.service_container = service_container
        self.event_bus = event_bus
        self.logger = logging.getLogger(self.__class__.__name__)

        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(5000)  # Update every 5 seconds

        self._setup_ui()
        self._update_status()

        self.logger.debug("Dashboard page initialized")

    def _setup_ui(self) -> None:
        """Setup the dashboard UI."""
        # Create main widget for ScrollArea
        main_widget = QWidget()
        layout = QVBoxLayout(main_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # Title
        title_label = QLabel("AI Twitch Co-Host Dashboard")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Status section
        status_frame = self._create_status_section()
        layout.addWidget(status_frame)

        # Activity section
        activity_frame = self._create_activity_section()
        layout.addWidget(activity_frame)

        # Quick actions section
        actions_frame = self._create_actions_section()
        layout.addWidget(actions_frame)

        # Add stretch to push everything to the top
        layout.addStretch()

        # Set the main widget for ScrollArea
        self.setWidget(main_widget)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

    def _create_status_section(self) -> QFrame:
        """Create the status indicators section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        frame.setLineWidth(1)

        layout = QVBoxLayout(frame)

        # Section title
        title = QLabel("System Status")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)

        # Status grid
        grid = QGridLayout()

        # Application status
        self.app_status_label = QLabel("🟢 Application: Running")
        grid.addWidget(QLabel("Application:"), 0, 0)
        grid.addWidget(self.app_status_label, 0, 1)

        # Twitch connection status
        self.twitch_status_label = QLabel("🔴 Twitch: Disconnected")
        grid.addWidget(QLabel("Twitch:"), 1, 0)
        grid.addWidget(self.twitch_status_label, 1, 1)

        # AI service status
        self.ai_status_label = QLabel("🟡 AI Service: Standby")
        grid.addWidget(QLabel("AI Service:"), 2, 0)
        grid.addWidget(self.ai_status_label, 2, 1)

        # Audio service status
        self.audio_status_label = QLabel("🟡 Audio: Standby")
        grid.addWidget(QLabel("Audio:"), 3, 0)
        grid.addWidget(self.audio_status_label, 3, 1)

        layout.addLayout(grid)

        return frame

    def _create_activity_section(self) -> QFrame:
        """Create the recent activity section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        frame.setLineWidth(1)

        layout = QVBoxLayout(frame)

        # Section title
        title = QLabel("Recent Activity")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)

        # Activity log (placeholder)
        self.activity_label = QLabel("No recent activity")
        self.activity_label.setStyleSheet("color: #666666;")
        layout.addWidget(self.activity_label)

        return frame

    def _create_actions_section(self) -> QFrame:
        """Create the quick actions section."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Raised)
        frame.setLineWidth(1)

        layout = QVBoxLayout(frame)

        # Section title
        title = QLabel("Quick Actions")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        layout.addWidget(title)

        # Action buttons (placeholder)
        actions_layout = QHBoxLayout()

        # Placeholder for action buttons
        placeholder_label = QLabel("Quick action buttons will be available here")
        placeholder_label.setStyleSheet("color: #666666;")
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        actions_layout.addWidget(placeholder_label)

        layout.addLayout(actions_layout)

        return frame

    def _update_status(self) -> None:
        """Update the status indicators."""
        try:
            # Update application status
            self.app_status_label.setText("🟢 Application: Running")

            # Check Twitch service status
            if self.service_container.is_registered('TwitchService'):
                twitch_service = self.service_container.resolve('TwitchService')
                if hasattr(twitch_service, 'is_connected') and twitch_service.is_connected():
                    self.twitch_status_label.setText("🟢 Twitch: Connected")
                else:
                    self.twitch_status_label.setText("🔴 Twitch: Disconnected")
            else:
                self.twitch_status_label.setText("🔴 Twitch: Not Available")

            # Check AI service status
            if self.service_container.is_registered('AIService'):
                self.ai_status_label.setText("🟢 AI Service: Ready")
            else:
                self.ai_status_label.setText("🔴 AI Service: Not Available")

            # Check Audio service status
            if self.service_container.is_registered('AudioService'):
                self.audio_status_label.setText("🟢 Audio: Ready")
            else:
                self.audio_status_label.setText("🔴 Audio: Not Available")

        except Exception as e:
            self.logger.error(f"Error updating dashboard status: {e}")

    def cleanup(self) -> None:
        """Clean up dashboard resources."""
        if self.status_timer:
            self.status_timer.stop()
        self.logger.debug("Dashboard page cleaned up")
