"""
Tests for AI service data models.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from ai_twitch_cohost.services.ai.models import (
    AIProviderType, AIModelInfo, AIRequest, AIResponse, AIError,
    UsageStats, RateLimitInfo, ProviderStatus, CostBreakdown,
    ModelPerformance
)


class TestAIProviderType:
    """Test AIProviderType enum."""
    
    def test_provider_types(self):
        """Test provider type values."""
        assert AIProviderType.OPENROUTER == "openrouter"
        assert AIProviderType.DEEPSEEK == "deepseek"
        assert AIProviderType.OLLAMA == "ollama"


class TestAIModelInfo:
    """Test AIModelInfo model."""
    
    def test_model_info_creation(self):
        """Test creating model info."""
        model = AIModelInfo(
            id="gpt-4",
            name="GPT-4",
            description="OpenAI's GPT-4 model",
            context_length=8192,
            pricing={"prompt": 0.03, "completion": 0.06},
            capabilities=["chat", "reasoning"],
            provider="openrouter"
        )
        
        assert model.id == "gpt-4"
        assert model.name == "GPT-4"
        assert model.context_length == 8192
        assert model.available is True  # Default value
        assert "chat" in model.capabilities
    
    def test_model_info_minimal(self):
        """Test creating model info with minimal data."""
        model = AIModelInfo(
            id="test-model",
            name="Test Model",
            provider="test"
        )
        
        assert model.id == "test-model"
        assert model.description is None
        assert model.capabilities == []
        assert model.available is True


class TestAIRequest:
    """Test AIRequest model."""
    
    def test_request_creation(self):
        """Test creating AI request."""
        request = AIRequest(
            prompt="Hello, world!",
            model="gpt-4",
            max_tokens=100,
            temperature=0.7,
            user_id="user123"
        )
        
        assert request.prompt == "Hello, world!"
        assert request.model == "gpt-4"
        assert request.max_tokens == 100
        assert request.temperature == 0.7
        assert request.user_id == "user123"
        assert request.conversation_history == []
    
    def test_request_minimal(self):
        """Test creating minimal AI request."""
        request = AIRequest(prompt="Test")
        
        assert request.prompt == "Test"
        assert request.model is None
        assert request.conversation_history == []


class TestAIResponse:
    """Test AIResponse model."""
    
    def test_response_creation(self):
        """Test creating AI response."""
        response = AIResponse(
            content="Hello there!",
            model="gpt-4",
            provider="openrouter",
            tokens_used=50,
            cost=0.001,
            response_time_ms=1500
        )
        
        assert response.content == "Hello there!"
        assert response.model == "gpt-4"
        assert response.provider == "openrouter"
        assert response.tokens_used == 50
        assert response.cost == 0.001
        assert response.response_time_ms == 1500
        assert response.metadata == {}


class TestAIError:
    """Test AIError model."""
    
    def test_error_creation(self):
        """Test creating AI error."""
        error = AIError(
            error_type="rate_limited",
            message="Rate limit exceeded",
            code="429",
            provider="openrouter",
            model="gpt-4",
            retryable=True,
            retry_after=60
        )
        
        assert error.error_type == "rate_limited"
        assert error.message == "Rate limit exceeded"
        assert error.code == "429"
        assert error.provider == "openrouter"
        assert error.retryable is True
        assert error.retry_after == 60


class TestUsageStats:
    """Test UsageStats model."""
    
    def test_usage_stats_creation(self):
        """Test creating usage statistics."""
        stats = UsageStats(
            provider="openrouter",
            model="gpt-4",
            total_requests=100,
            successful_requests=95,
            failed_requests=5,
            total_tokens=50000,
            total_cost=15.50,
            average_response_time_ms=1200.5
        )
        
        assert stats.provider == "openrouter"
        assert stats.model == "gpt-4"
        assert stats.total_requests == 100
        assert stats.successful_requests == 95
        assert stats.failed_requests == 5
        assert stats.total_tokens == 50000
        assert stats.total_cost == 15.50
        assert stats.average_response_time_ms == 1200.5
        assert isinstance(stats.created_at, datetime)
    
    def test_usage_stats_defaults(self):
        """Test usage statistics with default values."""
        stats = UsageStats(provider="test", model="test-model")
        
        assert stats.total_requests == 0
        assert stats.successful_requests == 0
        assert stats.failed_requests == 0
        assert stats.total_tokens == 0
        assert stats.total_cost == 0.0
        assert stats.average_response_time_ms == 0.0
        assert stats.last_used is None


class TestRateLimitInfo:
    """Test RateLimitInfo model."""
    
    def test_rate_limit_info_creation(self):
        """Test creating rate limit info."""
        rate_limit = RateLimitInfo(
            requests_per_minute=60,
            tokens_per_minute=50000,
            requests_remaining=45,
            tokens_remaining=30000,
            reset_time=datetime.now()
        )
        
        assert rate_limit.requests_per_minute == 60
        assert rate_limit.tokens_per_minute == 50000
        assert rate_limit.requests_remaining == 45
        assert rate_limit.tokens_remaining == 30000
        assert isinstance(rate_limit.reset_time, datetime)


class TestProviderStatus:
    """Test ProviderStatus model."""
    
    def test_provider_status_creation(self):
        """Test creating provider status."""
        status = ProviderStatus(
            provider="openrouter",
            status="online",
            error_message=None,
            available_models=["gpt-4", "gpt-3.5-turbo"]
        )
        
        assert status.provider == "openrouter"
        assert status.status == "online"
        assert status.error_message is None
        assert "gpt-4" in status.available_models
        assert isinstance(status.last_check, datetime)


class TestCostBreakdown:
    """Test CostBreakdown model."""
    
    def test_cost_breakdown_creation(self):
        """Test creating cost breakdown."""
        cost = CostBreakdown(
            provider="openrouter",
            model="gpt-4",
            prompt_tokens=100,
            completion_tokens=50,
            prompt_cost=0.003,
            completion_cost=0.003,
            total_cost=0.006
        )
        
        assert cost.provider == "openrouter"
        assert cost.model == "gpt-4"
        assert cost.prompt_tokens == 100
        assert cost.completion_tokens == 50
        assert cost.prompt_cost == 0.003
        assert cost.completion_cost == 0.003
        assert cost.total_cost == 0.006
        assert cost.currency == "USD"
        assert isinstance(cost.timestamp, datetime)


class TestModelPerformance:
    """Test ModelPerformance model."""
    
    def test_model_performance_creation(self):
        """Test creating model performance metrics."""
        performance = ModelPerformance(
            model="gpt-4",
            provider="openrouter",
            average_response_time_ms=1500.0,
            success_rate=0.95,
            cost_per_token=0.00006,
            quality_score=0.9,
            sample_size=100
        )
        
        assert performance.model == "gpt-4"
        assert performance.provider == "openrouter"
        assert performance.average_response_time_ms == 1500.0
        assert performance.success_rate == 0.95
        assert performance.cost_per_token == 0.00006
        assert performance.quality_score == 0.9
        assert performance.sample_size == 100
        assert isinstance(performance.last_updated, datetime)
    
    def test_model_performance_defaults(self):
        """Test model performance with default values."""
        performance = ModelPerformance(
            model="test-model",
            provider="test"
        )
        
        assert performance.average_response_time_ms == 0.0
        assert performance.success_rate == 0.0
        assert performance.cost_per_token == 0.0
        assert performance.quality_score is None
        assert performance.sample_size == 0
